import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';

import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../app/design_system/design_system.dart';
import '../widgets/shared_hero_components.dart';
import '../widgets/dashboard_components.dart';
import '../widgets/apps_page_components.dart';
import '../widgets/timeline/timeline_components.dart';
import '../widgets/common_ui_components.dart';
import '../widgets/app_list_widget.dart';
import '../widgets/gaming_app_bar.dart';
import '../widgets/analytics_page.dart';
import '../providers/service_providers.dart';
import '../providers/state_providers.dart';
import '../providers/search_filter_providers.dart';
import '../providers/poster_providers.dart';
import '../widgets/timeline/timeline_header.dart';

// Page definitions with their app bar properties
class AppPage {
  final String title;
  final IconData icon;
  final IconData navIcon;
  final String navLabel;
  final Widget Function() builder;

  const AppPage({
    required this.title,
    required this.icon,
    required this.navIcon,
    required this.navLabel,
    required this.builder,
  });
}

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> with TickerProviderStateMixin {
  late TabController _tabController;
  int _selectedIndex = 0;
  bool _isCurrentlyDesktop = false;

  late final List<AppPage> _pages = [
    AppPage(
      title: 'Gaming Dashboard',
      icon: Icons.dashboard_rounded,
      navIcon: Icons.dashboard_rounded,
      navLabel: 'Dashboard',
      builder: () => _buildDashboard(),
    ),
    AppPage(
      title: 'Game Library',
      icon: Icons.videogame_asset_rounded,
      navIcon: Icons.videogame_asset_rounded,
      navLabel: 'Games',
      builder: () => _buildAppsPage(),
    ),
    AppPage(
      title: 'Gaming Timeline',
      icon: Icons.timeline_rounded,
      navIcon: Icons.timeline_rounded,
      navLabel: 'Timeline',
      builder: () => _buildTimelinePage(),
    ),
    AppPage(
      title: 'Gaming Analytics',
      icon: Icons.analytics_rounded,
      navIcon: Icons.analytics_rounded,
      navLabel: 'Analytics',
      builder: () => _buildStatisticsPage(),
    ),
  ];

  late final List<NavigationItem> _navigationItems = _pages.map((page) => NavigationItem(
    title: page.title,
    icon: page.icon,
    navIcon: page.navIcon,
    navLabel: page.navLabel,
  )).toList();

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: _pages.length, vsync: this);

    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _selectedIndex = _tabController.index;
        });
      }
    });

    // Initialize connection to backend
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(timeTrackerRepositoryProvider).connect();
      // Set initial desktop state
      _isCurrentlyDesktop = _shouldUseDesktopLayout(context);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    ref.read(timeTrackerRepositoryProvider).disconnect();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentPage = _pages[_selectedIndex];

    // Use hysteresis to prevent flickering at breakpoint
    final shouldBeDesktop = _shouldUseDesktopLayout(context);
    if (shouldBeDesktop != _isCurrentlyDesktop) {
      // Only update if we're clearly past the hysteresis threshold
      final width = MediaQuery.of(context).size.width;
      if ((shouldBeDesktop && width > UIConstants.tabletBreakpoint + UIConstants.breakpointHysteresis) ||
          (!shouldBeDesktop && width < UIConstants.tabletBreakpoint - UIConstants.breakpointHysteresis)) {
        _isCurrentlyDesktop = shouldBeDesktop;
        // Ensure tab controller stays in sync without triggering animation
        if (_tabController.index != _selectedIndex) {
          _tabController.index = _selectedIndex;
        }
      }
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: GamingAppBar(
        title: currentPage.title,
        icon: currentPage.icon,
      ),
      body: _isCurrentlyDesktop ? _buildDesktopLayout() : _buildMobileLayout(),
      bottomNavigationBar: _isCurrentlyDesktop ? null : _buildBottomNavigation(),
    );
  }

  Widget _buildDesktopLayout() {
    return _buildPageContent(_selectedIndex);
  }

  Widget _buildMobileLayout() {
    return TabBarView(
      controller: _tabController,
      children: _pages.asMap().entries.map((entry) =>
        _buildPageContent(entry.key)
      ).toList(),
    );
  }

  Widget _buildPageContent(int index) {
    final currentPage = _pages[index];
    return currentPage.builder();
  }

  Widget _buildDashboard() {
    final trackingStatus = ref.watch(trackingStatusProvider);
    final apps = ref.watch(appsProvider);
    final recentApps = ref.watch(recentAppsProvider);
    final postersAsyncValue = ref.watch(postersProvider);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      color: isDark ? theme.darkBg : theme.colorScheme.surface,
      child: Column(
        children: [
          // Gaming Hero Section with Navigation
          SharedHeroComponents.buildGamingHeroWithNavigation(
            isDark: isDark,
            theme: theme,
            ref: ref,
            navigationItems: _navigationItems,
            selectedIndex: _selectedIndex,
            onNavigationTap: (index) => setState(() => _selectedIndex = index),
            isDesktop: _isCurrentlyDesktop,
          ),

          // Main Dashboard Content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(
                MediaQuery.of(context).size.width < DesignTokens.breakpointMobile
                  ? DesignTokens.spacingS
                  : DesignTokens.spacingL
              ),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
                  child: ModernColumn(
                    spacing: MediaQuery.of(context).size.width < DesignTokens.breakpointMobile
                      ? DesignTokens.spacingS
                      : DesignTokens.spacingL,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Quick Stats Grid
                      DashboardComponents.buildEnhancedStatsGrid(trackingStatus, apps, isDark, theme),

                      // Recent Games with Gaming Theme
                      DashboardComponents.buildGamingAppsSection(
                        recentApps,
                        isDark,
                        theme,
                        () => _tabController.animateTo(1)
                      ),

                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppsPage() {
    final filteredApps = ref.watch(filteredAppsProvider);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      color: isDark ? theme.darkBg : theme.colorScheme.surface,
      child: Column(
        children: [
          // Gaming Hero Section with Navigation
          SharedHeroComponents.buildGamingHeroWithNavigation(
            isDark: isDark,
            theme: theme,
            ref: ref,
            navigationItems: _navigationItems,
            selectedIndex: _selectedIndex,
            onNavigationTap: (index) => setState(() => _selectedIndex = index),
            isDesktop: _isCurrentlyDesktop,
          ),

          // Main Apps Content
          Expanded(
            child: Center(
              child: ConstrainedBox(
                constraints: BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
                child: Column(
                  children: [
                    // Enhanced Header Section with Frosted Glass
                    AppsPageComponents.buildGamingAppsHeader(isDark, theme, ref),

                    // Apps List with view toggle
                    Expanded(
                      child: filteredApps.when(
                        data: (appList) {
                          return appList.isEmpty
                              ? AppsPageComponents.buildEmptyGamesLibrary(isDark, theme, ref)
                              : Center(
                                  child: ConstrainedBox(
                                    constraints: BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
                                    child: Padding(
                                      padding: EdgeInsets.only(top:
                                        MediaQuery.of(context).size.width < DesignTokens.breakpointMobile
                                          ? DesignTokens.spacingS
                                          : DesignTokens.spacingL
                                      ),
                                      child: AppListWidget(
                                        apps: appList,
                                        showViewToggle: true,
                                      ),
                                    ),
                                  ),
                                );
                        },
                        loading: () => const Center(child: CircularProgressIndicator()),
                        error: (error, _) => CommonUIComponents.buildErrorState('Failed to load gaming library'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelinePage() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return ModernContainer(
      child: ModernColumn(
        children: [
          // Gaming Hero Section with Navigation
          SharedHeroComponents.buildGamingHeroWithNavigation(
            isDark: isDark,
            theme: theme,
            ref: ref,
            navigationItems: _navigationItems,
            selectedIndex: _selectedIndex,
            onNavigationTap: (index) => setState(() => _selectedIndex = index),
            isDesktop: _isCurrentlyDesktop,
          ),

          // Main Timeline Content
          Expanded(
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
              child: ModernColumn(
              children: [
                // Timeline Filter Header
                const TimelineHeader(),

                // Timeline List
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(top:
                      MediaQuery.of(context).size.width < DesignTokens.breakpointMobile
                        ? DesignTokens.spacingS
                        : DesignTokens.spacingL
                    ),
                    child: Center(
                      child: const TimelineComponents(),
                    ),
                  ),
                ),
              ],
            ),
            ),
          ),
        ],
      )
    );
  }

  Widget _buildStatisticsPage() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      color: isDark ? theme.darkBg : theme.colorScheme.surface,
      child: Column(
        children: [
          // Gaming Hero Section with Navigation
          SharedHeroComponents.buildGamingHeroWithNavigation(
            isDark: isDark,
            theme: theme,
            ref: ref,
            navigationItems: _navigationItems,
            selectedIndex: _selectedIndex,
            onNavigationTap: (index) => setState(() => _selectedIndex = index),
            isDesktop: _isCurrentlyDesktop,
          ),

          // Main Statistics Content
          Expanded(
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
              child: const AnalyticsPage(),
            ),
          ),
        ],
      ),
    );
  }



  Widget? _buildBottomNavigation() {
    if (_isCurrentlyDesktop) return null;

    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return ClipRRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          decoration: BoxDecoration(
            color: isDark
              ? Colors.black.withValues(alpha: 0.4)
              : theme.colorScheme.surface.withValues(alpha: 0.9),
            border: Border(
              top: BorderSide(
                color: isDark
                  ? Colors.white.withValues(alpha: 0.1)
                  : theme.colorScheme.outline.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
          ),
          child: BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            currentIndex: _selectedIndex,
            onTap: (index) {
              setState(() {
                _selectedIndex = index;
              });
              _tabController.animateTo(index);
            },
            backgroundColor: Colors.transparent,
            elevation: 0,
            selectedItemColor: theme.steamAccent,
            unselectedItemColor: isDark
              ? Colors.white.withValues(alpha: 0.6)
              : theme.colorScheme.onSurfaceVariant,
            items: _pages.map((page) => BottomNavigationBarItem(
              icon: Icon(page.navIcon),
              label: page.navLabel,
            )).toList(),
          ),
        ),
      ),
    );
  }

  bool _shouldUseDesktopLayout(BuildContext context) {
    return MediaQuery.of(context).size.width > UIConstants.tabletBreakpoint;
  }
}
