import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';
import '../providers/theme_provider.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/design_system/design_system.dart';
import '../../app/theme/app_theme.dart';
import 'connection_status_widget.dart';
import 'settings_dialog.dart';

/// A flexible gaming-themed app bar that can display any title and icon.
///
/// Example usage:
/// ```dart
/// GamingAppBar(
///   title: 'Settings',
///   icon: Icons.settings,
/// )
///
/// GamingAppBar(
///   title: 'Profile',
///   icon: Icons.person,
/// )
///
/// GamingAppBar() // Uses default 'Gaming Hub' with games icon
/// ```
class GamingAppBar extends ConsumerWidget implements PreferredSizeWidget {
  final String title;
  final IconData icon;

  const GamingAppBar({
    super.key,
    this.title = 'Gaming Hub',
    this.icon = Icons.games,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      title: Row(
        children: [
          Container(
            padding: EdgeInsets.all(DesignTokens.spacingM),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark ? [
                  AppTheme.primaryColor.withOpacity(0.3),
                  AppTheme.accentColor.withOpacity(0.2),
                ] : [
                  theme.colorScheme.primary.withOpacity(0.2),
                  theme.colorScheme.secondary.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(DesignTokens.radiusM),
            ),
            child: Icon(
              icon,
              color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
              size: UIConstants.iconM,
            ),
          ),
          SizedBox(width: DesignTokens.spacingM),
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
              fontWeight: FontWeight.w700,
            ),
          ),
        ],
      ),
      actions: [
        ConnectionStatusWidget(
          isCompact: MediaQuery.of(context).size.width < 600, // Mobile gets icon-only
        ),
        _buildTopBarButton(
          Icons.brightness_6,
          () => ref.read(themeModeProvider.notifier).toggleTheme(),
          isDark,
          theme,
          context,
        ),
        _buildTopBarButton(
          Icons.settings,
          () => _showSettingsDialog(context),
          isDark,
          theme,
          context,
        ),
        SizedBox(width: DesignTokens.spacingM),
      ],
    );
  }

  Widget _buildTopBarButton(IconData icon, VoidCallback onPressed, bool isDark, ThemeData theme, BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    
    return Container(
      margin: EdgeInsets.only(right: DesignTokens.spacingM),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: isDark
                ? Colors.white.withOpacity(0.1)
                : Colors.black.withOpacity(0.1),
              borderRadius: BorderRadius.circular(DesignTokens.radiusM),
              border: Border.all(
                color: isDark
                  ? Colors.white.withOpacity(0.2)
                  : Colors.black.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: IconButton(
              iconSize: isMobile ? 20.0 : 24.0, // Smaller icons on mobile
              padding: EdgeInsets.all(isMobile ? 8.0 : 12.0), // Tighter padding on mobile
              constraints: BoxConstraints(
                minWidth: isMobile ? 36.0 : 48.0, // Smaller touch target on mobile
                minHeight: isMobile ? 36.0 : 48.0,
              ),
              icon: Icon(
                icon,
                color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
              ),
              onPressed: onPressed,
            ),
          ),
        ),
      ),
    );
  }

  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const SettingsDialog(),
    );
  }
}
