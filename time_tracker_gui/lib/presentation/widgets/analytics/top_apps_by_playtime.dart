import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../app/design_system/design_system.dart';
import '../../../util/time_util.dart';
import '../../providers/state_providers.dart';
import '../shared_hero_components.dart';
import '../../../data/models/app_model.dart';

class PlayTimePerWeekdaySection extends ConsumerWidget {
  final bool isMobile;
  final bool isDark;

  const PlayTimePerWeekdaySection({
    super.key,
    required this.isMobile,
    required this.isDark,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the statistics provider for real data
    final statisticsAsync = ref.watch(statisticsProvider);

    return statisticsAsync.when(
      data: (statistics) {
        // Sort apps by total duration (descending)
        final sortedStats = List<AppStatistics>.from(statistics)
          ..sort((a, b) => b.totalDuration.compareTo(a.totalDuration));

        // Use all apps instead of just top 5
        final topApps = sortedStats;

        // Find the maximum duration for normalization
        final maxDuration = topApps.isNotEmpty ? topApps.first.totalDuration : 1;

        return BaseCard(
          variant: CardVariant.elevated,
          padding: EdgeInsets.all(isMobile ? DesignTokens.spacingM : DesignTokens.spacingL),
          child: ConstrainedBox(
            constraints: BoxConstraints(maxHeight: 400),
            child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Top Apps by Playtime',
                  style: TextStyle(
                    fontSize: isMobile ? DesignTokens.fontSize16 : DesignTokens.fontSize18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? AppColors.neutral100 : AppColors.neutral900,
                  ),
                ),
                SizedBox(height: DesignTokens.spacingXS),
                Text(
                  'Total time per app',
                  style: TextStyle(
                    fontSize: isMobile ? DesignTokens.fontSize12 : DesignTokens.fontSize14,
                    color: isDark ? AppColors.neutral400 : AppColors.neutral600,
                  ),
                ),
                SizedBox(height: isMobile ? DesignTokens.spacingM : DesignTokens.spacingL),
                _buildTopAppsList(isMobile, isDark, topApps, maxDuration),
              ],
            ),
          ),
        ),
      );
      },
      loading: () => BaseCard(
        variant: CardVariant.elevated,
        padding: EdgeInsets.all(DesignTokens.spacingM),
        child: ConstrainedBox(
          constraints: BoxConstraints(maxHeight: 400),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Top Apps by Playtime'),
                SizedBox(height: DesignTokens.spacingS),
                Text('Loading...'),
              ],
            ),
          ),
        ),
      ),
      error: (error, stackTrace) => BaseCard(
        variant: CardVariant.elevated,
        padding: EdgeInsets.all(DesignTokens.spacingM),
        child: ConstrainedBox(
          constraints: BoxConstraints(maxHeight: 400),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Top Apps by Playtime'),
                SizedBox(height: DesignTokens.spacingS),
                Text('Error loading data'),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTopAppsList(bool isMobile, bool isDark, List<AppStatistics> topApps, int maxDuration) {
    return Column(
      children: topApps.map((stat) {
        final appName = stat.app.name ?? stat.app.productName ?? 'Unknown App';
        final durationHours = (stat.totalDuration / 3600.0);
        final formattedDuration = TimeUtil.formatDurationFromMinutesAsHours(stat.totalDuration);
        final progress = maxDuration > 0 ? stat.totalDuration / maxDuration : 0;

        return Padding(
          padding: EdgeInsets.only(bottom: isMobile ? DesignTokens.spacingS : DesignTokens.spacingM),
          child: Row(
            children: [
              SizedBox(
                width: isMobile ? 100 : 150,
                child: Text(
                  appName,
                  style: TextStyle(
                    fontSize: isMobile ? DesignTokens.fontSize12 : DesignTokens.fontSize14,
                    fontWeight: FontWeight.w500,
                    color: isDark ? AppColors.neutral100 : AppColors.neutral900,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(DesignTokens.radiusFull),
                  child: LinearProgressIndicator(
                    value: progress.clamp(0.0, 1.0).toDouble(),
                    backgroundColor: AppColors.primary500.withValues(alpha: 0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary500),
                    minHeight: isMobile ? 8 : 10,
                  ),
                ),
              ),
              SizedBox(width: DesignTokens.spacingM),
              Text(
                formattedDuration,
                style: TextStyle(
                  fontSize: isMobile ? DesignTokens.fontSize12 : DesignTokens.fontSize14,
                  fontWeight: FontWeight.bold,
                  color: isDark ? AppColors.neutral400 : AppColors.neutral600,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}