import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../app/design_system/design_system.dart';
import '../../../util/time_util.dart';
import '../../providers/app_timeline_providers.dart';
import '../shared_hero_components.dart';

class PlaytimeChart extends ConsumerWidget {
  final bool isMobile;
  final bool isDark;

  const PlaytimeChart({
    super.key,
    required this.isMobile,
    required this.isDark,
 });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the timeline provider for real data
    final timelineAsync = ref.watch(allTimeTimelineProvider);

    return timelineAsync.when(
      data: (timeline) {
        // Calculate total playtime for all time
        int totalPlaytime = 0;

        // Calculate playtime per year
        final playtimePerYear = <int, int>{};

        // Process timeline data to calculate playtime per year
        for (final session in timeline) {
          if (session.date != null && session.duration != null) {
            totalPlaytime += session.duration!;

            // Get year
            final year = session.date!.year;
            playtimePerYear[year] = (playtimePerYear[year] ?? 0) + session.duration!;
          }
        }

        // Format total playtime
        final formattedTotalTime = TimeUtil.formatDurationFromMinutesAsHours(totalPlaytime);

        return BaseCard(
          variant: CardVariant.elevated,
          padding: EdgeInsets.all(isMobile ? DesignTokens.spacingS : DesignTokens.spacingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Playtime',
                        style: TextStyle(
                          fontSize: isMobile ? DesignTokens.fontSize16 : DesignTokens.fontSize18,
                          fontWeight: FontWeight.bold,
                          color: isDark ? AppColors.neutral100 : AppColors.neutral900,
                        ),
                      ),
                      Row(
                        children: [
                          Text(
                            'Per Year',
                            style: TextStyle(
                              fontSize: isMobile ? DesignTokens.fontSize12 : DesignTokens.fontSize14,
                              color: isDark ? AppColors.neutral400 : AppColors.neutral600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  Text(
                    formattedTotalTime,
                    style: TextStyle(
                      fontSize: isMobile ? DesignTokens.fontSize24 : DesignTokens.fontSize28,
                      fontWeight: FontWeight.bold,
                      color: isDark ? AppColors.neutral100 : AppColors.neutral900,
                    ),
                  ),
                ],
              ),
              SizedBox(height: isMobile ? DesignTokens.spacingS : DesignTokens.spacingL),
              // Custom chart implementation with real data
              _buildYearlyChartWithData(isMobile, isDark, playtimePerYear),
              SizedBox(height: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM),
              // Year labels
              _buildYearLabels(isMobile, playtimePerYear),
            ],
          ),
        );
      },
      loading: () => BaseCard(
        variant: CardVariant.elevated,
        padding: EdgeInsets.all(isMobile ? DesignTokens.spacingS : DesignTokens.spacingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Playtime'),
            SizedBox(height: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM),
            Text('Loading...'),
          ],
        ),
      ),
      error: (error, stackTrace) => BaseCard(
        variant: CardVariant.elevated,
        padding: EdgeInsets.all(isMobile ? DesignTokens.spacingS : DesignTokens.spacingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Playtime'),
            SizedBox(height: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM),
            Text('Error loading data'),
          ],
        ),
      ),
    );
  }

  Widget _buildYearlyChartWithData(bool isMobile, bool isDark, Map<int, int> playtimePerYear) {
    final chartHeight = isMobile ? 140.0 : 180.0;

    return SizedBox(
      height: chartHeight,
      child: CustomPaint(
        painter: YearlyPlaytimeChartPainter(isDark: isDark, playtimePerYear: playtimePerYear),
        size: const Size(double.infinity, 180),
      ),
    );
  }

  Widget _buildYearLabels(bool isMobile, Map<int, int> playtimePerYear) {
    final labelStyle = TextStyle(
      fontSize: isMobile ? DesignTokens.fontSize10 : DesignTokens.fontSize12,
      fontWeight: FontWeight.bold,
      color: isDark ? AppColors.neutral400 : AppColors.neutral600,
    );

    // Get sorted years
    final years = playtimePerYear.keys.toList()..sort();

    if (years.isEmpty) {
      return const SizedBox.shrink();
    }

    // Use a simple Row with MainAxisAlignment.spaceBetween for better alignment
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: years.map((year) => Text('$year', style: labelStyle)).toList(),
    );
  }
}

class YearlyPlaytimeChartPainter extends CustomPainter {
  final bool isDark;
  final Map<int, int> playtimePerYear;

  YearlyPlaytimeChartPainter({required this.isDark, required this.playtimePerYear});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    final gradient = LinearGradient(
      colors: [AppColors.primary500, AppColors.primary500.withValues(alpha: 0.0)],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    );

    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final shader = gradient.createShader(rect);
    paint.shader = shader;

    // Draw the chart path using real data
    final path = Path();

    // Get sorted years
    final years = playtimePerYear.keys.toList()..sort();

    if (years.isEmpty) {
      // If no data, draw a flat line at the bottom
      path.moveTo(0, size.height);
      path.lineTo(size.width, size.height);
    } else {
      // Find the maximum playtime to normalize the values
      final maxPlaytime = playtimePerYear.values.reduce((a, b) => a > b ? a : b);

      // Calculate the width for each year segment (matching MainAxisAlignment.spaceBetween)
      final segmentCount = years.length > 1 ? years.length - 1 : 1;
      final segmentWidth = size.width / segmentCount;

      // Draw lines connecting the data points
      for (int i = 0; i < years.length; i++) {
        final year = years[i];
        final playtime = playtimePerYear[year] ?? 0;

        // Calculate the height of the point (inverted because 0 is at the top)
        final pointHeight = maxPlaytime > 0 ? (playtime / maxPlaytime) * size.height : 0;
        final y = size.height - pointHeight;
        final x = i * segmentWidth;

        if (i == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }
    }

    // End point
    path.lineTo(size.width, size.height);

    canvas.drawPath(path, paint);

    // Fill the area under the curve
    final fillPaint = Paint()
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.fill;

    final fillPath = Path();
    fillPath.addPath(path, Offset.zero);
    fillPath.lineTo(size.width, size.height);
    fillPath.lineTo(0, size.height);
    fillPath.close();

    canvas.drawPath(fillPath, fillPaint);

    // Draw value labels for each year
    if (years.isNotEmpty) {
      final maxPlaytime = playtimePerYear.values.reduce((a, b) => a > b ? a : b);
      final segmentCount = years.length > 1 ? years.length - 1 : 1;
      final segmentWidth = size.width / segmentCount;
      final textPainter = TextPainter(
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
      );

      for (int i = 0; i < years.length; i++) {
        final year = years[i];
        final playtime = playtimePerYear[year] ?? 0;
        final pointHeight = maxPlaytime > 0 ? (playtime / maxPlaytime) * size.height : 0;
        final y = size.height - pointHeight;
        final x = i * segmentWidth;

        // Format the playtime
        final formattedTime = TimeUtil.formatDurationFromMinutesAsHours(playtime);

        // Create text span
        textPainter.text = TextSpan(
          text: formattedTime,
          style: TextStyle(
            color: isDark ? AppColors.neutral100 : AppColors.neutral900,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        );

        // Layout and paint the text
        textPainter.layout();
        textPainter.paint(canvas, Offset(x - textPainter.width / 2, y - textPainter.height - 5));
      }

      // Draw circles at each data point
      final circlePaint = Paint()
        ..color = AppColors.primary500
        ..style = PaintingStyle.fill;

      for (int i = 0; i < years.length; i++) {
        final year = years[i];
        final playtime = playtimePerYear[year] ?? 0;
        final pointHeight = maxPlaytime > 0 ? (playtime / maxPlaytime) * size.height : 0;
        final y = size.height - pointHeight;
        final x = i * segmentWidth;

        // Draw circle at data point
        canvas.drawCircle(Offset(x, y), 5, circlePaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}