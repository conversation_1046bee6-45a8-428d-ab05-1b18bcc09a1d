import 'package:flutter/material.dart';
import '../../../app/design_system/design_system.dart';

class AnalyticsTabSelector extends StatefulWidget {
  final int selectedTab;
  final Function(int) onTabChanged;
  final bool isMobile;

  const AnalyticsTabSelector({
    super.key,
    required this.selectedTab,
    required this.onTabChanged,
    required this.isMobile,
  });

  @override
  State<AnalyticsTabSelector> createState() => _AnalyticsTabSelectorState();
}

class _AnalyticsTabSelectorState extends State<AnalyticsTabSelector> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: widget.isMobile ? DesignTokens.spacingM : DesignTokens.spacingL,
        vertical: DesignTokens.spacingM,
      ),
      child: Container(
        height: widget.isMobile ? 40 : 48,
        decoration: BoxDecoration(
          color: AppColors.darkSurface3,
          borderRadius: BorderRadius.circular(DesignTokens.radiusFull),
        ),
        child: Row(
          children: [
            _buildTabButton('Overview', 0),
            _buildTabButton('Games', 1),
          ],
        ),
      ),
    );
  }

  Widget _buildTabButton(String label, int index) {
    final isSelected = widget.selectedTab == index;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          widget.onTabChanged(index);
        },
        child: Container(
          margin: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: isSelected
                ? AppColors.primary500
                : Colors.transparent,
            borderRadius: BorderRadius.circular(DesignTokens.radiusFull),
          ),
          child: Center(
            child: Text(
              label,
              style: TextStyle(
                fontSize: widget.isMobile ? DesignTokens.fontSize12 : DesignTokens.fontSize14,
                fontWeight: FontWeight.bold,
                color: isSelected
                    ? AppColors.neutral0
                    : isDark
                        ? AppColors.neutral400
                        : AppColors.neutral600,
              ),
            ),
          ),
        ),
      ),
    );
  }
}