import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../app/design_system/design_system.dart';
import '../../../data/models/app_model.dart';
import '../../../util/time_util.dart';
import '../../providers/state_providers.dart';
import '../shared_hero_components.dart';

class StatsCards extends ConsumerWidget {
  final bool isMobile;
  final bool isDark;

  const StatsCards({
    super.key,
    required this.isMobile,
    required this.isDark,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statisticsAsync = ref.watch(statisticsProvider);

    return statisticsAsync.when(
      data: (statistics) {
        // Calculate total playtime from all apps
        int totalPlaytime = 0;
        for (final stat in statistics) {
          totalPlaytime += stat.totalDuration;
        }

        // Format playtime
        final formattedPlaytime = TimeUtil.formatDurationFromMinutesAsHours(totalPlaytime);

        return Row(
          children: [
            Expanded(
              child: BaseCard(
                variant: CardVariant.elevated,
                padding: EdgeInsets.all(isMobile ? DesignTokens.spacingS : DesignTokens.spacingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Total Playtime',
                      style: TextStyle(
                        fontSize: isMobile ? DesignTokens.fontSize12 : DesignTokens.fontSize14,
                        fontWeight: FontWeight.w500,
                        color: isDark ? AppColors.neutral400 : AppColors.neutral600,
                      ),
                    ),
                    SizedBox(height: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM),
                    Text(
                      formattedPlaytime,
                      style: TextStyle(
                        fontSize: isMobile ? DesignTokens.fontSize24 : DesignTokens.fontSize28,
                        fontWeight: FontWeight.bold,
                        color: isDark ? AppColors.neutral100 : AppColors.neutral900,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(width: isMobile ? DesignTokens.spacingS : DesignTokens.spacingL),
            Expanded(
              child: BaseCard(
                variant: CardVariant.elevated,
                padding: EdgeInsets.all(isMobile ? DesignTokens.spacingS : DesignTokens.spacingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Total Apps',
                      style: TextStyle(
                        fontSize: isMobile ? DesignTokens.fontSize12 : DesignTokens.fontSize14,
                        fontWeight: FontWeight.w500,
                        color: isDark ? AppColors.neutral400 : AppColors.neutral600,
                      ),
                    ),
                    SizedBox(height: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM),
                    Text(
                      '${statistics.length}',
                      style: TextStyle(
                        fontSize: isMobile ? DesignTokens.fontSize24 : DesignTokens.fontSize28,
                        fontWeight: FontWeight.bold,
                        color: isDark ? AppColors.neutral100 : AppColors.neutral900,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
      loading: () => Row(
        children: [
          Expanded(
            child: BaseCard(
              variant: CardVariant.elevated,
              padding: EdgeInsets.all(isMobile ? DesignTokens.spacingS : DesignTokens.spacingL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Total Playtime'),
                  SizedBox(height: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM),
                  Text('Loading...'),
                ],
              ),
            ),
          ),
          SizedBox(width: isMobile ? DesignTokens.spacingS : DesignTokens.spacingL),
          Expanded(
            child: BaseCard(
              variant: CardVariant.elevated,
              padding: EdgeInsets.all(isMobile ? DesignTokens.spacingS : DesignTokens.spacingL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Total Apps'),
                  SizedBox(height: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM),
                  Text('Loading...'),
                ],
              ),
            ),
          ),
        ],
      ),
      error: (error, stackTrace) => Row(
        children: [
          Expanded(
            child: BaseCard(
              variant: CardVariant.elevated,
              padding: EdgeInsets.all(isMobile ? DesignTokens.spacingS : DesignTokens.spacingL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Total Playtime'),
                  SizedBox(height: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM),
                  Text('Error'),
                ],
              ),
            ),
          ),
          SizedBox(width: isMobile ? DesignTokens.spacingS : DesignTokens.spacingL),
          Expanded(
            child: BaseCard(
              variant: CardVariant.elevated,
              padding: EdgeInsets.all(isMobile ? DesignTokens.spacingS : DesignTokens.spacingL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Total Apps'),
                  SizedBox(height: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM),
                  Text('Error'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}