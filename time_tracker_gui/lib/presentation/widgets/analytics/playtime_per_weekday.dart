import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../app/design_system/design_system.dart';
import '../../../util/time_util.dart';
import '../../providers/app_timeline_providers.dart';
import '../shared_hero_components.dart';

class PlaytimePerWeekday extends ConsumerWidget {
  final bool isMobile;
  final bool isDark;

  const PlaytimePerWeekday({
    super.key,
    required this.isMobile,
    required this.isDark,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the timeline provider for real data
    final timelineAsync = ref.watch(allTimeTimelineProvider);

    return timelineAsync.when(
      data: (timeline) {
        // Calculate playtime per day of week (0 = Monday, 6 = Sunday)
        final playtimePerDay = List<int>.filled(7, 0);

        // Process timeline data to calculate playtime per day
        for (final session in timeline) {
          if (session.date != null && session.duration != null) {
            // Get day of week (0 = Monday, 6 = Sunday)
            final dayOfWeek = session.date!.weekday - 1; // DateTime.weekday returns 1-7, we want 0-6
            if (dayOfWeek >= 0 && dayOfWeek < 7) {
              playtimePerDay[dayOfWeek] += session.duration!;
            }
          }
        }

        // Find the maximum playtime for normalization
        final maxPlaytime = playtimePerDay.reduce((a, b) => a > b ? a : b);

        return BaseCard(
          variant: CardVariant.elevated,
          padding: EdgeInsets.all(isMobile ? DesignTokens.spacingM : DesignTokens.spacingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Playtime',
                style: TextStyle(
                  fontSize: isMobile ? DesignTokens.fontSize16 : DesignTokens.fontSize18,
                  fontWeight: FontWeight.bold,
                  color: isDark ? AppColors.neutral100 : AppColors.neutral900,
                ),
              ),
              SizedBox(height: DesignTokens.spacingXS),
              Text(
                'Per Day of Week',
                style: TextStyle(
                  fontSize: isMobile ? DesignTokens.fontSize12 : DesignTokens.fontSize14,
                  color: isDark ? AppColors.neutral400 : AppColors.neutral600,
                ),
              ),
              SizedBox(height: isMobile ? DesignTokens.spacingM : DesignTokens.spacingL),
              _buildDayOfWeekBars(isMobile, isDark, playtimePerDay, maxPlaytime),
            ],
          ),
        );
      },
      loading: () => BaseCard(
        variant: CardVariant.elevated,
        padding: EdgeInsets.all(DesignTokens.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Playtime'),
            SizedBox(height: DesignTokens.spacingS),
            Text('Loading...'),
          ],
        ),
      ),
      error: (error, stackTrace) => BaseCard(
        variant: CardVariant.elevated,
        padding: EdgeInsets.all(DesignTokens.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Playtime'),
            SizedBox(height: DesignTokens.spacingS),
            Text('Error loading data'),
          ],
        ),
      ),
    );
  }

  Widget _buildDayOfWeekBars(bool isMobile, bool isDark, List<int> playtimePerDay, int maxPlaytime) {
    return SizedBox(
      height: isMobile ? 140.0 : 180.0,
      child: Column(
        children: [
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                _buildDayBar('Mon', playtimePerDay[0], maxPlaytime, isMobile, isDark),
                _buildDayBar('Tue', playtimePerDay[1], maxPlaytime, isMobile, isDark),
                _buildDayBar('Wed', playtimePerDay[2], maxPlaytime, isMobile, isDark),
                _buildDayBar('Thu', playtimePerDay[3], maxPlaytime, isMobile, isDark),
                _buildDayBar('Fri', playtimePerDay[4], maxPlaytime, isMobile, isDark),
                _buildDayBar('Sat', playtimePerDay[5], maxPlaytime, isMobile, isDark),
                _buildDayBar('Sun', playtimePerDay[6], maxPlaytime, isMobile, isDark),
              ],
            ),
          ),
          SizedBox(height: DesignTokens.spacingXS),
          // Add value labels below the bars
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: List.generate(7, (index) {
              final formattedTime = TimeUtil.formatDurationFromMinutesAsHours(playtimePerDay[index]);
              return Text(
                formattedTime,
                style: TextStyle(
                  fontSize: isMobile ? DesignTokens.fontSize10 : DesignTokens.fontSize12,
                  fontWeight: FontWeight.bold,
                  color: isDark ? AppColors.neutral400 : AppColors.neutral600,
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildDayBar(String label, int playtime, int maxPlaytime, bool isMobile, bool isDark) {
    // Calculate height ratio (0.1 minimum to ensure visibility)
    final heightRatio = maxPlaytime > 0 ? (playtime / maxPlaytime).clamp(0.1, 1.0) : 0.1;

    return Expanded(
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: isMobile ? 2.0 : 4.0),
        child: Column(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: heightRatio == 1.0
                      ? AppColors.primary500
                      : AppColors.primary500.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.vertical(top: Radius.circular(DesignTokens.radiusFull)),
                ),
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Container(
                    height: heightRatio * (isMobile ? 100 : 140),
                    decoration: BoxDecoration(
                      color: heightRatio == 1.0
                          ? AppColors.primary500
                          : AppColors.primary500.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.vertical(
                        top: Radius.circular(DesignTokens.radiusFull),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(height: DesignTokens.spacingXS),
            Text(
              label,
              style: TextStyle(
                fontSize: isMobile ? DesignTokens.fontSize10 : DesignTokens.fontSize12,
                fontWeight: FontWeight.bold,
                color: isDark ? AppColors.neutral400 : AppColors.neutral600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}