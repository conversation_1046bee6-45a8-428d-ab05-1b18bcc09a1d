import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:io';
import '../../data/models/poster_model.dart';
import 'dart:ui';
import '../../data/models/app_model.dart';
import '../providers/state_providers.dart';
import '../providers/local_game_poster_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../app/design_system/design_system.dart';
import 'app_detail_view.dart';
import 'local_game_poster_widget.dart';
import '../providers/local_game_poster_providers.dart';

enum AppListViewMode { list, posters }

class AppListWidget extends ConsumerStatefulWidget {
  final List<AppModel> apps;
  final bool isCompact;
  final bool showViewToggle;

  const AppListWidget({
    super.key,
    required this.apps,
    this.isCompact = false,
    this.showViewToggle = true,
  });

  @override
  ConsumerState<AppListWidget> createState() => _AppListWidgetState();
}

class _AppListWidgetState extends ConsumerState<AppListWidget> {
  AppListViewMode _viewMode = AppListViewMode.list;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final sessionCounts = ref.watch(sessionCountsProvider);

    if (widget.apps.isEmpty) {
      return Center(
        child: Container(
          padding: EdgeInsets.all(DesignTokens.spacingXL),
          child: _buildEmptyLibraryCard(context, isDark, theme),
        ),
      );
    }

    return Container(
      constraints: BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
      child: Column(
        mainAxisSize: widget.isCompact ? MainAxisSize.min : MainAxisSize.max,
        children: [
          // View toggle (only show if not compact and toggle is enabled)
          if (!widget.isCompact && widget.showViewToggle) ...[
            _buildViewToggle(theme, isDark),
            SizedBox(height: DesignTokens.spacingM),
          ],

          // Content based on view mode
          if (widget.isCompact)
            _viewMode == AppListViewMode.list
                ? _buildListView(sessionCounts, isDark, theme, ref)
                : _buildPosterView(isDark, theme)
          else
            Expanded(
              child: _viewMode == AppListViewMode.list
                  ? _buildListView(sessionCounts, isDark, theme, ref)
                  : _buildPosterView(isDark, theme),
            ),
        ],
      ),
    );
  }

  Widget _buildViewToggle(ThemeData theme, bool isDark) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: DesignTokens.spacingM),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Container(
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[800] : Colors.grey[100],
              borderRadius: BorderRadius.circular(DesignTokens.radiusL),
              border: Border.all(
                color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildToggleButton(
                  icon: Icons.list,
                  label: 'List',
                  isSelected: _viewMode == AppListViewMode.list,
                  onTap: () => setState(() => _viewMode = AppListViewMode.list),
                  theme: theme,
                  isDark: isDark,
                ),
                _buildToggleButton(
                  icon: Icons.grid_view,
                  label: 'Posters',
                  isSelected: _viewMode == AppListViewMode.posters,
                  onTap: () => setState(() => _viewMode = AppListViewMode.posters),
                  theme: theme,
                  isDark: isDark,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleButton({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required ThemeData theme,
    required bool isDark,
  }) {
    return isSelected
        ? PrimaryButton(
            onPressed: onTap,
            icon: icon,
            size: ButtonSize.small,
            child: Text(label),
          )
        : OutlinedModernButton(
            onPressed: onTap,
            icon: icon,
            size: ButtonSize.small,
            child: Text(label),
          );
  }

  Widget _buildListView(AsyncValue<Map<int, int>> sessionCounts, bool isDark, ThemeData theme, WidgetRef ref) {
    final isMobile = MediaQuery.of(context).size.width < DesignTokens.breakpointMobile;
    
    return ListView.builder(
      shrinkWrap: widget.isCompact,
      physics: widget.isCompact ? const NeverScrollableScrollPhysics() : null,
      padding: EdgeInsets.symmetric(horizontal: DesignTokens.spacingM),
      itemCount: widget.apps.length,
      itemBuilder: (context, index) {
        final app = widget.apps[index];
        return Center(
          child: ConstrainedBox(
            constraints: BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
            child: _buildAppListItem(app, isDark, theme, ref, context, sessionCounts, index, isMobile),
          ),
        );
      },
    );
  }

  Widget _buildPosterView(bool isDark, ThemeData theme) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < DesignTokens.breakpointMobile;

    return GridView.builder(
      padding: EdgeInsets.all(DesignTokens.spacingM),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getPosterCrossAxisCount(),
        childAspectRatio: 0.7, // Poster aspect ratio
        crossAxisSpacing: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM,
        mainAxisSpacing: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM,
      ),
      itemCount: widget.apps.length,
      itemBuilder: (context, index) {
        final app = widget.apps[index];
        // Wrap the LocalGamePosterWidget with a container for better visual separation
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isDark ? const Color(0xFF2A3A32) : const Color(0xFF3D5248),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: isDark
                    ? Colors.black.withOpacity(0.2)
                    : Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: LocalGamePosterWidget(
            app: app,
            onTap: () => _showAppDetails(context, app),
          ),
        );
      },
    );
  }

  int _getPosterCrossAxisCount() {
    final width = MediaQuery.of(context).size.width;
    if (width > DesignTokens.breakpointDesktop) {
      return 6;
    } else if (width > DesignTokens.breakpointMobile) {
      return 4;
    } else {
      return 3;
    }
  }

  int _getSessionCount(AppModel app, AsyncValue<Map<int, int>> sessionCounts) {
    return sessionCounts.when(
      data: (countsMap) => countsMap[app.id] ?? 0,
      loading: () => 0,
      error: (_, __) => 0,
    );
  }

  Widget _buildAppListItem(AppModel app, bool isDark, ThemeData theme, WidgetRef ref, BuildContext context, AsyncValue<Map<int, int>> sessionCounts, int index, bool isMobile) {
    final sessionCount = _getSessionCount(app, sessionCounts);
    final gameName = _getGameName(app);
    final posterAsync = ref.watch(cachedPosterProvider(gameName));
    final imagePathAsync = ref.watch(cachedImagePathProvider(gameName));

    final backgroundColor = const Color(0xFF111714);

    return Container(
      margin: EdgeInsets.only(
        bottom: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM,
        top: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingS,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? const Color(0xFF2A3A32) : const Color(0xFF3D5248),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withOpacity(0.3)
                : Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showAppDetails(context, app),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: EdgeInsets.all(isMobile ? 8 : 12),
            child: Row(
              children: [
                // Circular app image with ring
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
                    border: Border.all(
                      color: const Color(0x1AFFFFFF),
                      width: 2,
                    ),
                  ),
                  child: ClipOval(
                    child: _buildPosterImage(ref, gameName, posterAsync, imagePathAsync, isDark),
                  ),
                ),
                SizedBox(width: isMobile ? 8 : 16),
                // Game name and session count
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        app.name ?? 'Unknown Game',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          height: 1.5,
                        ),
                      ),
                      Text(
                        '$sessionCount sessions',
                        style: const TextStyle(
                          color: Color(0xFF9EB7A8),
                          fontSize: 14,
                          fontWeight: FontWeight.normal,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),
                // Play time and delete button
                Row(
                  children: [
                    Text(
                      _formatDuration(app.duration ?? 0),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.normal,
                        height: 1.5,
                      ),
                    ),
                    SizedBox(width: isMobile ? 2 : 8),
                    Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        color: isDark
                            ? AppTheme.neutralGray700.withOpacity(0.8)
                            : AppTheme.neutralGray200.withOpacity(0.8),
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: Icon(
                          Icons.delete,
                          size: 18,
                          color: isDark ? AppTheme.errorColor : AppTheme.errorColor.withOpacity(0.8),
                        ),
                        onPressed: () => _showDeleteConfirmation(context, ref, app),
                        tooltip: 'Remove from library',
                        padding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyLibraryCard(BuildContext context, bool isDark, ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(theme.spacingXXL.w),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(theme.radiusXXL),
        border: Border.all(
          color: isDark
            ? theme.steamAccent.withOpacity(0.2)
            : theme.colorScheme.outline.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
              ? Colors.black.withOpacity(0.3)
              : Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(theme.spacingXL.w),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isDark
                ? theme.steamAccent.withOpacity(0.15)
                : theme.colorScheme.primaryContainer,
              border: Border.all(
                color: theme.steamAccent.withOpacity(0.3),
                width: 1.5,
              ),
            ),
            child: Icon(
              Icons.videogame_asset,
              size: DesignTokens.iconXL * 1.8,
              color: theme.steamAccent,
            ),
          ),
          SizedBox(height: theme.spacingL.h),
          ShaderMask(
            shaderCallback: (bounds) => isDark
              ? theme.accentGradient.createShader(bounds)
              : LinearGradient(
                  colors: [theme.colorScheme.primary, theme.colorScheme.secondary],
                ).createShader(bounds),
            child: Text(
              'Your Game Library Awaits',
              style: theme.textTheme.displaySmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w800,
                letterSpacing: -0.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: theme.spacingM.h),
          Text(
            'Start your gaming session to see your library here.',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: isDark
                ? Colors.white.withOpacity(0.7)
                : theme.colorScheme.onSurfaceVariant,
              height: 1.6,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: theme.spacingXL.h),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: theme.spacingL.w,
              vertical: theme.spacingM.h,
            ),
            decoration: BoxDecoration(
              color: isDark
                ? theme.steamAccent.withOpacity(0.1)
                : theme.colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(theme.radiusL),
              border: Border.all(
                color: theme.steamAccent.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.play_circle_outline,
                  color: theme.steamAccent,
                  size: DesignTokens.iconM,
                ),
                SizedBox(width: theme.spacingS.w),
                Text(
                  'Ready to track your games',
                  style: theme.textTheme.labelLarge?.copyWith(
                    color: theme.steamAccent,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, WidgetRef ref, AppModel app) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDark ? theme.cardBg : theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(theme.radiusXL),
        ),
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(theme.spacingS.w),
              decoration: BoxDecoration(
                color: theme.colorScheme.error.withOpacity(0.2),
                borderRadius: BorderRadius.circular(theme.radiusM),
              ),
              child: Icon(
                Icons.delete_outline,
                color: theme.colorScheme.error,
                size: DesignTokens.iconM,
              ),
            ),
            SizedBox(width: theme.spacingM.w),
            Text(
              'Remove Game',
              style: theme.textTheme.titleLarge?.copyWith(
                color: isDark ? Colors.white : theme.colorScheme.onSurface,
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
        ),
        content: Text(
          'Are you sure you want to remove "${app.name}" from your gaming library? This action cannot be undone.',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: isDark
              ? Colors.white.withOpacity(0.8)
              : theme.colorScheme.onSurfaceVariant,
            height: 1.5,
          ),
        ),
        actions: [
          OutlinedModernButton(
            onPressed: () => Navigator.of(context).pop(),
            size: ButtonSize.small,
            child: const Text('Cancel'),
          ),
          DangerButton(
            onPressed: () {
              final appName = app.name;
              if (appName != null && appName.isNotEmpty) {
                ref.read(appsProvider.notifier).deleteApp(appName);
                Navigator.of(context).pop();
              }
            },
            size: ButtonSize.small,
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  String _formatDuration(int minutes) {
    final hours = minutes ~/ 60;
    final mins = minutes % 60;

    if (hours > 0) {
      return '${hours}h ${mins}m';
    } else if (minutes > 0) {
      return '${minutes}m';
    } else {
      return '${mins}m';
    }
  }


  Widget _buildPosterImage(
    WidgetRef ref,
    String gameName,
    AsyncValue<Poster?> posterAsync,
    AsyncValue<String?> imagePathAsync,
    bool isDark,
  ) {
    return posterAsync.when(
      data: (poster) {
        if (poster != null && poster.hasImage) {
          return imagePathAsync.when(
            data: (imagePath) {
              if (imagePath != null) {
                // Use cached local image
                return Image.file(
                  File(imagePath),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // Fallback to network image if local file fails
                    return _buildNetworkImage(poster.backgroundImage, isDark);
                  },
                );
              } else {
                // Use network image
                return _buildNetworkImage(poster.backgroundImage, isDark);
              }
            },
            loading: () => _buildNetworkImage(poster.backgroundImage, isDark),
            error: (error, stackTrace) => _buildNetworkImage(poster.backgroundImage, isDark),
          );
        } else {
          return _buildGamePlaceholder(isDark);
        }
      },
      loading: () => _buildLoadingPlaceholder(isDark),
      error: (error, stackTrace) => _buildGamePlaceholder(isDark),
    );
  }

  Widget _buildNetworkImage(String imageUrl, bool isDark) {
    return Image.network(
      imageUrl,
      fit: BoxFit.cover,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return _buildLoadingPlaceholder(isDark);
      },
      errorBuilder: (context, error, stackTrace) {
        return _buildGamePlaceholder(isDark);
      },
    );
  }

  Widget _buildLoadingPlaceholder(bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildGamePlaceholder(bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
      ),
      child: const Center(
        child: Icon(
          Icons.videogame_asset,
          color: Colors.white70,
        ),
      ),
    );
  }

  String _getGameName(AppModel app) {
      String gameName = '';
    if (app.productName?.isNotEmpty == true) {
      gameName = app.productName!;
    } else if (app.name?.isNotEmpty == true) {
      gameName = app.name!;
    }

    // Clean up the game name for better search results
    gameName = gameName
        .replaceAll(RegExp(r'\.exe$', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();

    return gameName;
  }

  void _showAppDetails(BuildContext context, AppModel app) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            AppDetailView(app: app),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOutCubic;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }
}
