import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../app/design_system/design_system.dart';
import '../../util/time_util.dart';
import '../providers/daily_session_provider.dart';
import '../../data/services/daily_session_service.dart';

class DashboardCard extends StatelessWidget {
  final String title;
  final Widget child;
  final Widget? trailing;
  final bool isCompact;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? padding;
  final Widget? icon;

  const DashboardCard({
    super.key,
    required this.title,
    required this.child,
    this.trailing,
    this.isCompact = false,
    this.backgroundColor,
    this.padding,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < DesignTokens.breakpointMobile;

    final responsivePadding = padding ?? EdgeInsets.all(
      isCompact
        ? (isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM)
        : (isMobile ? DesignTokens.spacingS : DesignTokens.spacingL)
    );

    return Card(
      elevation: 0,
      color: backgroundColor ?? Theme.of(context).colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusL),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: responsivePadding,
        child: Center(
          child: ConstrainedBox(
            constraints: BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(context),
                if (!isCompact) SizedBox(height: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM),
                child,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        if (icon != null) ...[
          icon!,
          SizedBox(width: DesignTokens.spacingS),
        ],
        Expanded(
          child: Text(
            title,
            style: isCompact
                ? Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  )
                : Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
          ),
        ),
        if (trailing != null) trailing!,
      ],
    );
  }
}

class CommunicationStatusCard extends StatelessWidget {
  final String connectionType;
  final Duration averageResponseTime;
  final int successfulRequests;
  final int failedRequests;
  final DateTime? lastUpdate;

  const CommunicationStatusCard({
    super.key,
    required this.connectionType,
    required this.averageResponseTime,
    required this.successfulRequests,
    required this.failedRequests,
    this.lastUpdate,
  });

  @override
  Widget build(BuildContext context) {
    final totalRequests = successfulRequests + failedRequests;
    final successRate = totalRequests > 0 ? (successfulRequests / totalRequests * 100) : 0.0;

    Color statusColor = successRate > 95
        ? Colors.green
        : successRate > 80
            ? Colors.orange
            : Colors.red;

    return DashboardCard(
      title: 'Communication Status',
      icon: Icon(
        connectionType == 'WebSocket' ? Icons.wifi : Icons.sync,
        color: statusColor,
        size: DesignTokens.iconS,
      ),
      child: Column(
        children: [
          // Connection type and response time
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Connection',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    connectionType,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: statusColor,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'Response Time',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    '${averageResponseTime.inMilliseconds}ms',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ),

          SizedBox(height: DesignTokens.spacingM),

          // Success rate indicator
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Success Rate',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    '${successRate.toStringAsFixed(1)}%',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: statusColor,
                    ),
                  ),
                ],
              ),
              SizedBox(height: DesignTokens.spacingS),
              LinearProgressIndicator(
                value: successRate / 100,
                backgroundColor: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                valueColor: AlwaysStoppedAnimation<Color>(statusColor),
              ),
            ],
          ),

          if (lastUpdate != null) ...[
            SizedBox(height: DesignTokens.spacingM),
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 14.sp,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                SizedBox(width: DesignTokens.spacingS),
                Text(
                  'Last update: ${_formatTime(lastUpdate!)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ],

          SizedBox(height: DesignTokens.spacingM),

          // Request statistics
          Container(
            padding: EdgeInsets.all(DesignTokens.spacingM),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(DesignTokens.radiusS),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  context,
                  'Successful',
                  successfulRequests.toString(),
                  Colors.green,
                ),
                Container(
                  width: 1,
                  height: 20.h,
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
                _buildStatItem(
                  context,
                  'Failed',
                  failedRequests.toString(),
                  Colors.red,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w700,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: 10.sp,
          ),
        ),
      ],
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return '${difference.inSeconds}s ago';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else {
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }
}
