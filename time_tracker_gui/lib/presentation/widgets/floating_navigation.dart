import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../app/design_system/design_system.dart';

class FloatingNavigation extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onDestinationSelected;
  final List<FloatingNavDestination> destinations;
  final bool isDark;
  final ThemeData theme;

  const FloatingNavigation({
    super.key,
    required this.selectedIndex,
    required this.onDestinationSelected,
    required this.destinations,
    required this.isDark,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: DesignTokens.spacingXL,
      left: DesignTokens.spacingXL,
      right: DesignTokens.spacingXL,
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 600),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(DesignTokens.radiusXXL),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: DesignTokens.spacingM,
                  vertical: DesignTokens.spacingM,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: isDark ? [
                      Colors.black.withOpacity(0.8),
                      Colors.black.withOpacity(0.6),
                    ] : [
                      Colors.white.withOpacity(0.9),
                      Colors.white.withOpacity(0.7),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(DesignTokens.radiusXXL),
                  border: Border.all(
                    color: isDark
                      ? Colors.white.withOpacity(0.2)
                      : Colors.black.withOpacity(0.1),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(isDark ? 0.5 : 0.1),
                      blurRadius: 30,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: destinations.asMap().entries.map((entry) {
                    final index = entry.key;
                    final destination = entry.value;
                    final isSelected = selectedIndex == index;
                    
                    return GestureDetector(
                      onTap: () => onDestinationSelected(index),
                      child: AnimatedContainer(
                        duration: DesignTokens.durationNormal,
                        curve: Curves.easeInOut,
                        padding: EdgeInsets.symmetric(
                          horizontal: isSelected ? DesignTokens.spacingL : DesignTokens.spacingM,
                          vertical: DesignTokens.spacingM,
                        ),
                        decoration: BoxDecoration(
                          gradient: isSelected ? theme.steamGradient : null,
                          borderRadius: BorderRadius.circular(DesignTokens.radiusXL),
                          boxShadow: isSelected ? [
                            BoxShadow(
                              color: theme.steamAccent.withOpacity(0.4),
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ] : null,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              destination.icon,
                              size: DesignTokens.iconL,
                              color: isSelected 
                                ? Colors.white
                                : (isDark ? Colors.white.withOpacity(0.7) : Colors.black.withOpacity(0.7)),
                            ),
                            if (isSelected) ...[
                              SizedBox(width: DesignTokens.spacingS),
                              AnimatedDefaultTextStyle(
                                duration: DesignTokens.durationNormal,
                                style: theme.textTheme.labelLarge?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ) ?? const TextStyle(),
                                child: Text(destination.label),
                              ),
                            ],
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class FloatingNavDestination {
  final IconData icon;
  final String label;

  const FloatingNavDestination({
    required this.icon,
    required this.label,
  });
}

// Alternative: Compact Floating Navigation (smaller, more subtle)
class CompactFloatingNavigation extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onDestinationSelected;
  final List<FloatingNavDestination> destinations;
  final bool isDark;
  final ThemeData theme;

  const CompactFloatingNavigation({
    super.key,
    required this.selectedIndex,
    required this.onDestinationSelected,
    required this.destinations,
    required this.isDark,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: DesignTokens.spacingXL + 60, // Below the hero section
      right: DesignTokens.spacingXL,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.radiusXL),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: Container(
            padding: EdgeInsets.all(DesignTokens.spacingM),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark ? [
                  Colors.black.withOpacity(0.7),
                  Colors.black.withOpacity(0.5),
                ] : [
                  Colors.white.withOpacity(0.8),
                  Colors.white.withOpacity(0.6),
                ],
              ),
              borderRadius: BorderRadius.circular(DesignTokens.radiusXL),
              border: Border.all(
                color: isDark
                  ? Colors.white.withOpacity(0.2)
                  : Colors.black.withOpacity(0.1),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(isDark ? 0.4 : 0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: destinations.asMap().entries.map((entry) {
                final index = entry.key;
                final destination = entry.value;
                final isSelected = selectedIndex == index;
                
                return Padding(
                  padding: EdgeInsets.symmetric(vertical: DesignTokens.spacingXS),
                  child: GestureDetector(
                    onTap: () => onDestinationSelected(index),
                    child: AnimatedContainer(
                      duration: DesignTokens.durationNormal,
                      padding: EdgeInsets.all(DesignTokens.spacingM),
                      decoration: BoxDecoration(
                        gradient: isSelected ? theme.steamGradient : null,
                        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
                        boxShadow: isSelected ? [
                          BoxShadow(
                            color: theme.steamAccent.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ] : null,
                      ),
                      child: Icon(
                        destination.icon,
                        size: DesignTokens.iconL,
                        color: isSelected 
                          ? Colors.white
                          : (isDark ? Colors.white.withOpacity(0.7) : Colors.black.withOpacity(0.7)),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }
} 
