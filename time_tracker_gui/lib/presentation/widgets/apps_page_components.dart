import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';
import '../providers/search_filter_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../app/design_system/design_system.dart';
import 'app_list_widget.dart';
import 'common_ui_components.dart';
import 'dialog_components.dart';
import 'common/search_filter_layout.dart';

class AppsPageComponents {
  static Widget buildGamingAppsHeader(bool isDark, ThemeData theme, WidgetRef ref) {
    final searchState = ref.watch(appSearchProvider);
    final hasActiveFilters = searchState.searchQuery.isNotEmpty ||
                           searchState.sortBy != AppSortBy.name ||
                           searchState.sortOrder != SortOrder.ascending;

    return SearchFilterLayout(
      searchWidget: ModernTextField(
        hintText: 'Search your gaming library...',
        controller: TextEditingController(text: searchState.searchQuery),
        onChanged: (value) => ref.read(appSearchProvider.notifier).updateSearchQuery(value),
        prefixIcon: const Icon(Icons.search),
        variant: InputVariant.filled,
      ),
      onFilterPressed: () => DialogComponents.showFilterDialog(ref.context, ref),
      onClearPressed: hasActiveFilters ? () => ref.read(appSearchProvider.notifier).clearAllFilters() : null,
      hasActiveFilters: hasActiveFilters,
      filterChips: hasActiveFilters ? Wrap(
        spacing: DesignTokens.spacingM,
        children: [
          if (searchState.searchQuery.isNotEmpty)
            buildFilterChip(
              'Search: "${searchState.searchQuery}"',
              () => ref.read(appSearchProvider.notifier).clearSearch(),
              isDark,
              theme,
            ),
          if (searchState.sortBy != AppSortBy.name)
            buildFilterChip(
              'Sort: ${_getSortByLabel(searchState.sortBy)}',
              () => ref.read(appSearchProvider.notifier).updateSortBy(AppSortBy.name),
              isDark,
              theme,
            ),
          if (searchState.sortOrder != SortOrder.ascending)
            buildFilterChip(
              'Order: ${_getSortOrderLabel(searchState.sortOrder)}',
              () => ref.read(appSearchProvider.notifier).updateSortOrder(SortOrder.ascending),
              isDark,
              theme,
            ),
        ],
      ) : null,
    );
  }

  static Widget buildFilterChip(String label, VoidCallback onRemove, bool isDark, ThemeData theme, {bool isMobile = false}) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM,
        vertical: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingS,
      ),
      decoration: BoxDecoration(
        color: isDark
          ? AppTheme.primaryColor.withOpacity(0.2)
          : theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(DesignTokens.radiusS),
        border: Border.all(
          color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingXS),
          GestureDetector(
            onTap: onRemove,
            child: Icon(
              Icons.close,
              size: 14,
              color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  static String _getSortByLabel(AppSortBy sortBy) {
    switch (sortBy) {
      case AppSortBy.name:
        return 'Name';
      case AppSortBy.duration:
        return 'Total Time';
      case AppSortBy.launches:
        return 'Times Played';
      case AppSortBy.lastUsed:
        return 'Last Played';
    }
  }

  static String _getSortOrderLabel(SortOrder order) {
    switch (order) {
      case SortOrder.ascending:
        return 'Ascending';
      case SortOrder.descending:
        return 'Descending';
    }
  }

  static Widget buildEmptyGamesLibrary(bool isDark, ThemeData theme, WidgetRef ref) {
    return Center(
      child: ModernContainer(
        padding: EdgeInsets.all(DesignTokens.spacingM),
        child: ModernColumn(
          spacing: DesignTokens.spacingS,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(DesignTokens.spacingS),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary500.withOpacity(0.2),
                    AppColors.secondary500.withOpacity(0.1),
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.games,
                size: 64,
                color: AppColors.primary500,
              ),
            ),
            Text(
              'No Games in Library',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w700,
                color: AppColors.neutral900,
              ),
            ),
            Text(
              'Start building your gaming library to track sessions\nand discover new adventures!',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.neutral600,
              ),
              textAlign: TextAlign.center,
            ),
            PrimaryButton(
              onPressed: () => DialogComponents.showAddAppDialog(ref.context, ref),
              icon: Icons.add,
              child: const Text('Add Your First Game'),
            ),
          ],
        ),
      ),
    );
  }
}