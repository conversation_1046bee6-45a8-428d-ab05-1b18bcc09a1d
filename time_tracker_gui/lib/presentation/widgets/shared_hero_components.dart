import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';
import 'dart:io';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../data/models/app_model.dart';
import '../providers/state_providers.dart';
import '../providers/rotating_poster_provider.dart';
import '../providers/local_game_poster_providers.dart';
import '../../util/time_util.dart';
import '../../app/design_system/design_system.dart';

class NavigationItem {
  final String title;
  final IconData icon;
  final IconData navIcon;
  final String navLabel;

  const NavigationItem({
    required this.title,
    required this.icon,
    required this.navIcon,
    required this.navLabel,
  });
}

class SharedHeroComponents {
  static Widget? buildGamingHeroSection(bool isDark, ThemeData theme, WidgetRef ref, {
    List<NavigationItem>? navigationItems,
    int? selectedIndex,
    Function(int)? onNavigationTap,
    bool isDesktop = false,
  }) {
    final trackingStatus = ref.watch(trackingStatusProvider);
    final rotatingPoster = ref.watch(rotatingPosterProvider);

    // Only show the hero section if there's an active gaming session
    final status = trackingStatus.asData?.value;

    return LayoutBuilder(
      builder: (context, constraints) {
        // Responsive hero height
        final screenWidth = constraints.maxWidth;
        double heroHeight;

        if (screenWidth < 600) {
          heroHeight = 200; // Mobile
        } else if (screenWidth < 900) {
          heroHeight = 250; // Tablet
        } else {
          heroHeight = 300; // Desktop
        }

        return Container(
          height: heroHeight,
          decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDark ? [
            AppTheme.primaryColor.withValues(alpha: 0.3),
            AppTheme.accentColor.withValues(alpha: 0.2),
            theme.darkBg.withValues(alpha: 0.8),
          ] : [
            theme.colorScheme.primary.withValues(alpha: 0.2),
            theme.colorScheme.secondary.withValues(alpha: 0.1),
            theme.colorScheme.surface.withValues(alpha: 0.9),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background poster image
          if (rotatingPoster.currentPoster?.hasImage == true)
            Positioned.fill(
              child: ClipRRect(
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    _buildPosterBackground(rotatingPoster, ref, isDark),
                    // Dark overlay for better text readability
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withValues(alpha: 0.3),
                            Colors.black.withValues(alpha: 0.6),
                            Colors.black.withValues(alpha: 0.8),
                          ],
                          stops: const [0.0, 0.6, 1.0],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Gradient overlay (fallback when no poster)
          if (rotatingPoster.currentPoster?.hasImage != true)
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    (isDark ? theme.darkBg : theme.colorScheme.surface).withValues(alpha: 0.3),
                    (isDark ? theme.darkBg : theme.colorScheme.surface).withValues(alpha: 0.8),
                  ],
                  stops: const [0.0, 0.7, 1.0],
                ),
              ),
            ),

          if (status != null && status.isTracking)
            // Main content with tracking status
            Positioned(
              bottom: navigationItems != null ? DesignTokens.spacingXL + 90 : DesignTokens.spacingXL + 60,
              left: DesignTokens.spacingL,
              right: DesignTokens.spacingL,
              child: Center(
                child: ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(DesignTokens.radiusXL),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 20, sigmaY: 15),
                      child: Container(
                        padding: EdgeInsets.all(UIConstants.spacingXL.w),
                        decoration: BoxDecoration(
                          color: isDark
                            ? Colors.white.withValues(alpha: 0.1)
                            : Colors.black.withValues(alpha: 0.05),
                          borderRadius: BorderRadius.circular(DesignTokens.radiusXL),
                          border: Border.all(
                            color: isDark
                              ? Colors.white.withValues(alpha: 0.3)
                              : Colors.black.withValues(alpha: 0.15),
                            width: 1,
                          ),
                        ),
                        child: IntrinsicWidth(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Tracking status section
                              trackingStatus.when(
                                data: (status) => _buildTrackingContent(status, isDark, theme),
                                loading: () => _buildLoadingContent(isDark, theme),
                                error: (error, _) => _buildErrorContent(isDark, theme),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),

          // Navigation inside hero banner (desktop only)
          if (navigationItems != null && isDesktop && selectedIndex != null && onNavigationTap != null)
            Positioned(
              bottom: 0,
              left: UIConstants.spacingL,
              right: UIConstants.spacingL,
              child: Center(
                child: ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
                  child: ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(DesignTokens.radiusXL),
                      topRight: Radius.circular(DesignTokens.radiusXL),
                    ),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 20, sigmaY: 15),
                        child: Container(
                          padding: EdgeInsets.all(screenWidth < 600 ? UIConstants.spacingS : UIConstants.spacingM),
                          decoration: BoxDecoration(
                            color: isDark
                              ? Colors.white.withValues(alpha: 0.1)
                              : Colors.black.withValues(alpha: 0.05),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(UIConstants.radiusXL),
                              topRight: Radius.circular(UIConstants.radiusXL),
                            ),
                          border: Border(
                            top: BorderSide(
                              color: isDark
                                ? Colors.white.withValues(alpha: 0.3)
                                : Colors.black.withValues(alpha: 0.15),
                              width: 1,
                            ),
                            left: BorderSide(
                              color: isDark
                                ? Colors.white.withValues(alpha: 0.3)
                                : Colors.black.withValues(alpha: 0.15),
                              width: 1,
                            ),
                            right: BorderSide(
                              color: isDark
                                ? Colors.white.withValues(alpha: 0.3)
                                : Colors.black.withValues(alpha: 0.15),
                              width: 1,
                            ),
                          ),
                        ),
                        child: IntrinsicWidth(
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: navigationItems.asMap().entries.map((entry) {
                              final index = entry.key;
                              final item = entry.value;
                              final isSelected = selectedIndex == index;

                              final isMobile = screenWidth < 600;

                              return Container(
                                margin: EdgeInsets.symmetric(horizontal: isMobile ? UIConstants.spacingXS : UIConstants.spacingS),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () => onNavigationTap(index),
                                    borderRadius: BorderRadius.circular(UIConstants.radiusM),
                                    child: AnimatedContainer(
                                      duration: UIConstants.animationFast,
                                      curve: Curves.easeInOut,
                                      height: isMobile ? UIConstants.buttonHeightS : UIConstants.buttonHeightM, // Consistent height
                                      padding: EdgeInsets.symmetric(
                                        horizontal: isMobile ? UIConstants.spacingXS : UIConstants.spacingS,
                                        vertical: isMobile ? UIConstants.spacingXS : UIConstants.spacingS,
                                      ),
                                      decoration: BoxDecoration(
                                        color: isSelected
                                          ? (isDark ? Colors.white.withValues(alpha: 0.15) : Colors.black.withValues(alpha: 0.1))
                                          : Colors.transparent,
                                        borderRadius: BorderRadius.circular(UIConstants.radiusM),
                                        border: isSelected ? Border.all(
                                          color: isDark
                                            ? Colors.white.withValues(alpha: 0.2)
                                            : Colors.black.withValues(alpha: 0.1),
                                          width: 1,
                                        ) : null,
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            item.navIcon,
                                            size: isMobile ? UIConstants.iconS : UIConstants.iconM,
                                            color: isSelected
                                              ? (isDark ? Colors.white : Colors.black)
                                              : (isDark ? Colors.white.withValues(alpha: 0.7) : Colors.black.withValues(alpha: 0.7)),
                                          ),
                                          if (!isMobile) SizedBox(width: UIConstants.spacingS),
                                          if (!isMobile)
                                            Text(
                                              item.navLabel,
                                              style: theme.textTheme.labelLarge?.copyWith(
                                                color: isSelected
                                                  ? (isDark ? Colors.white : Colors.black)
                                                  : (isDark ? Colors.white.withValues(alpha: 0.7) : Colors.black.withValues(alpha: 0.7)),
                                                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),

          // Poster loading indicator
          if (rotatingPoster.isLoading)
            Positioned(
              top: UIConstants.spacingL,
              right: UIConstants.spacingL,
              child: Container(
                padding: EdgeInsets.all(UIConstants.spacingS),
                decoration: BoxDecoration(
                  color: isDark ? Colors.black.withValues(alpha: 0.3) : Colors.white.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(UIConstants.radiusM),
                ),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      isDark ? Colors.white : Colors.black,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
      },
    );
  }

  static Widget buildGamingHeroWithNavigation({
    required bool isDark,
    required ThemeData theme,
    required WidgetRef ref,
    required List<NavigationItem> navigationItems,
    required int selectedIndex,
    required Function(int) onNavigationTap,
    required bool isDesktop,
  }) {
    return buildGamingHeroSection(
          isDark,
          theme,
          ref,
          navigationItems: navigationItems,
          selectedIndex: selectedIndex,
          onNavigationTap: onNavigationTap,
          isDesktop: isDesktop,
        ) ??
        const SizedBox.shrink();
  }

  static Widget _buildPosterBackground(RotatingPosterState rotatingPoster, WidgetRef ref, bool isDark) {
    if (rotatingPoster.currentApp == null || rotatingPoster.currentPoster == null) {
      return Container();
    }

    final gameName = _getGameName(rotatingPoster.currentApp!);
    final imagePathAsyncValue = ref.watch(cachedImagePathProvider(gameName));

    return imagePathAsyncValue.when(
      data: (imagePath) {
        if (imagePath != null) {
          return Image.file(
            File(imagePath),
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return _buildNetworkPosterImage(rotatingPoster.currentPoster!.backgroundImage);
            },
          );
        } else {
          return _buildNetworkPosterImage(rotatingPoster.currentPoster!.backgroundImage);
        }
      },
      loading: () => _buildNetworkPosterImage(rotatingPoster.currentPoster!.backgroundImage),
      error: (error, stackTrace) => _buildNetworkPosterImage(rotatingPoster.currentPoster!.backgroundImage),
    );
  }

  static Widget _buildNetworkPosterImage(String imageUrl) {
    return Image.network(
      imageUrl,
      fit: BoxFit.cover,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Container(
          color: Colors.grey.withValues(alpha: 0.3),
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return Container(color: Colors.grey.withValues(alpha: 0.3));
      },
    );
  }

  static Widget _buildTrackingContent(TrackingStatus status, bool isDark, ThemeData theme) {
    Color statusColor;
    String trackingStatus;
    IconData statusIcon;

    if (status.isTracking && !status.isPaused) {
      statusColor = AppTheme.successColor;
      trackingStatus = 'Currently tracking';
      statusIcon = Icons.play_circle_filled;
    } else if (status.isPaused) {
      statusColor = AppTheme.warningColor;
      trackingStatus = 'Currently tracking';
      statusIcon = Icons.pause_circle_filled;
    } else {
      trackingStatus = 'Last tracked';
      statusColor = AppTheme.neutralGray500;
      statusIcon = Icons.stop_circle;
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          // Status indicator
          Container(
            padding: EdgeInsets.all(UIConstants.spacingS),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  statusColor.withValues(alpha: 0.3),
                  statusColor.withValues(alpha: 0.2),
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: Icon(
              statusIcon,
              size: UIConstants.iconXL,
              color: statusColor,
            ),
          ),
          SizedBox(width: UIConstants.spacingXL),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (status.currentApp != null) ...[
                Text(
                  '$trackingStatus: ${status.currentApp}',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: UIConstants.spacingXS.h),
                Text(
                  'Session time: ${TimeUtil.formatDurationFromMinutes(status.currentSessionDuration)}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                  ),
                ),
              ] else ...[
                Text(
                  'No active gaming session',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  static Widget _buildLoadingContent(bool isDark, ThemeData theme) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(UIConstants.spacingM),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDark ? [
                AppTheme.primaryColor.withValues(alpha: 0.3),
                AppTheme.accentColor.withValues(alpha: 0.2),
              ] : [
                theme.colorScheme.primary.withValues(alpha: 0.2),
                theme.colorScheme.secondary.withValues(alpha: 0.1),
              ],
            ),
            shape: BoxShape.circle,
          ),
          child: SizedBox(
            width: UIConstants.iconXL,
            height: UIConstants.iconXL,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(
                isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
              ),
            ),
          ),
        ),
        SizedBox(width: UIConstants.spacingXL),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Loading Session...',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
              ),
            ),
            SizedBox(height: UIConstants.spacingS.h),
            Text(
              'Connecting to gaming tracker',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
              ),
            ),
          ],
        ),
      ],
    );
  }

  static Widget _buildErrorContent(bool isDark, ThemeData theme) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(UIConstants.spacingM),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppTheme.errorColor.withValues(alpha: 0.3),
                AppTheme.errorColor.withValues(alpha: 0.2),
              ],
            ),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.error_outline,
            size: UIConstants.iconXL,
            color: AppTheme.errorColor,
          ),
        ),
        SizedBox(width: UIConstants.spacingXL),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Connection Error',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
              ),
            ),
            SizedBox(height: UIConstants.spacingS.h),
            Text(
              'Unable to connect to gaming tracker',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
              ),
            ),
          ],
        ),
      ],
    );
  }

  static String _getGameName(AppModel app) {
    String gameName = '';

    if (app.productName?.isNotEmpty == true) {
      gameName = app.productName!;
    } else if (app.name?.isNotEmpty == true) {
      gameName = app.name!;
    }

    gameName = gameName
        .replaceAll(RegExp(r'\.exe$', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();

    return gameName;
  }
}
