import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_riverpod/legacy.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../data/models/app_model.dart';
import '../providers/state_providers.dart';
import '../providers/connection_status_providers.dart';
import '../providers/daily_session_provider.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/design_system/design_system.dart';
import '../../app/theme/app_theme.dart';
import '../../util/time_util.dart';

// Add a provider for tracking command loading states
final trackingCommandStateProvider = StateProvider<String?>((ref) => null);

class TrackingControls extends ConsumerStatefulWidget {
  final TrackingStatus status;

  const TrackingControls({
    super.key,
    required this.status,
  });

  @override
  ConsumerState<TrackingControls> createState() => _TrackingControlsState();
}

class _TrackingControlsState extends ConsumerState<TrackingControls>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Start pulse animation if tracking is active
    if (widget.status.isTracking && !widget.status.isPaused) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(TrackingControls oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update animation based on tracking state
    if (widget.status.isTracking && !widget.status.isPaused) {
      if (!_pulseController.isAnimating) {
        _pulseController.repeat(reverse: true);
      }
    } else {
      _pulseController.stop();
      _pulseController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
        child: Column(
          children: [
            _buildStatusIndicator(context),
            SizedBox(height: DesignTokens.spacingL),
            _buildCurrentApp(context),
            SizedBox(height: DesignTokens.spacingL),
            _buildSessionInfo(context),
            SizedBox(height: DesignTokens.spacingXL),
            _buildControlButtons(context, ref),
            SizedBox(height: UIConstants.spacingM.h),
            _buildCommandFeedback(context),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(BuildContext context) {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    if (widget.status.isTracking && !widget.status.isPaused) {
      statusColor = AppTheme.successColor;
      statusText = 'Tracking Active';
      statusIcon = Icons.play_circle_filled;
    } else if (widget.status.isPaused) {
      statusColor = AppTheme.warningColor;
      statusText = 'Tracking Paused';
      statusIcon = Icons.pause_circle_filled;
    } else {
      statusColor = AppTheme.neutralGray500;
      statusText = 'Tracking Stopped';
      statusIcon = Icons.stop_circle;
    }

    Widget statusWidget = Container(
      padding: EdgeInsets.symmetric(
        horizontal: UIConstants.spacingL.w,
        vertical: UIConstants.spacingM.h,
      ),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.08),
        borderRadius: BorderRadius.circular(DesignTokens.radiusXL),
        border: Border.all(
          color: statusColor.withOpacity(0.2),
          width: 1.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.all(UIConstants.spacingXS),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.15),
              shape: BoxShape.circle,
            ),
            child: Icon(
              statusIcon,
              color: statusColor,
              size: UIConstants.iconS.sp,
            ),
          ),
          SizedBox(width: UIConstants.spacingM.w),
          Text(
            statusText,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );

    // Add pulse animation for active tracking
    if (widget.status.isTracking && !widget.status.isPaused) {
      return AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: statusWidget,
          );
        },
      );
    }

    return statusWidget;
  }

  Widget _buildCurrentApp(BuildContext context) {
    if (widget.status.currentApp == null) {
      return Column(
        children: [
          Icon(
            Icons.apps,
            size: 32.sp,
            color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.5),
          ),
          SizedBox(height: UIConstants.spacingS.h),
          Text(
            'No app being tracked',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      );
    }

    return Column(
      children: [
        Text(
          'Currently Tracking',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        SizedBox(height: UIConstants.spacingXS.h),
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: UIConstants.spacingM.w,
            vertical: UIConstants.spacingS.h,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(DesignTokens.radiusM),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.apps,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                size: 18.sp,
              ),
              SizedBox(width: UIConstants.spacingS.w),
              Text(
                widget.status.currentApp!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSessionInfo(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildInfoItem(
          context,
          'Session Time',
          // Convert minutes to proper time format
          TimeUtil.formatDurationFromMinutes(widget.status.currentSessionDuration),
          Icons.timer,
        ),
        _buildInfoItem(
          context,
          'Started',
          widget.status.sessionStartTime != null
              ? _formatTime(widget.status.sessionStartTime!)
              : 'N/A',
          Icons.schedule,
        ),
      ],
    );
  }

  Widget _buildInfoItem(BuildContext context, String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 20.sp,
        ),
        SizedBox(height: UIConstants.spacingXS.h),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildControlButtons(BuildContext context, WidgetRef ref) {
    final commandState = ref.watch(trackingCommandStateProvider);

    return Wrap(
      spacing: UIConstants.spacingM.w,
      runSpacing: UIConstants.spacingS.h,
      alignment: WrapAlignment.center,
      children: [
        if (!widget.status.isTracking)
          _buildActionButton(
            context: context,
            onPressed: () => _executeCommand(ref, 'start', () async {
              await ref.read(trackingStatusProvider.notifier).startTracking();
            }),
            icon: Icons.play_arrow,
            label: 'Start Tracking',
            backgroundColor: AppTheme.successColor,
            foregroundColor: Colors.white,
            isLoading: commandState == 'start',
          ),

        if (widget.status.isTracking && !widget.status.isPaused)
          _buildActionButton(
            context: context,
            onPressed: () => _executeCommand(ref, 'pause', () async {
              await ref.read(trackingStatusProvider.notifier).pauseTracking();
            }),
            icon: Icons.pause,
            label: 'Pause',
            backgroundColor: AppTheme.warningColor,
            foregroundColor: Colors.white,
            isLoading: commandState == 'pause',
          ),

        if (widget.status.isTracking && widget.status.isPaused)
          _buildActionButton(
            context: context,
            onPressed: () => _executeCommand(ref, 'resume', () async {
              await ref.read(trackingStatusProvider.notifier).resumeTracking();
            }),
            icon: Icons.play_arrow,
            label: 'Resume',
            backgroundColor: AppTheme.successColor,
            foregroundColor: Colors.white,
            isLoading: commandState == 'resume',
          ),

        if (widget.status.isTracking)
          _buildActionButton(
            context: context,
            onPressed: () => _executeCommand(ref, 'stop', () async {
              await ref.read(trackingStatusProvider.notifier).stopTracking();
              await ref.read(todaySessionProvider.notifier).refresh();
            }),
            icon: Icons.stop,
            label: 'Stop',
            backgroundColor: AppTheme.errorColor,
            foregroundColor: Colors.white,
            isLoading: commandState == 'stop',
          ),
      ],
    );
  }

  Future<void> _executeCommand(WidgetRef ref, String command, Future<void> Function() action) async {
    ref.read(trackingCommandStateProvider.notifier).state = command;

    try {
      await action();
      // Show success feedback briefly
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      // Show error feedback
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to $command tracking: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      ref.read(trackingCommandStateProvider.notifier).state = null;
    }
  }

  Widget _buildActionButton({
    required BuildContext context,
    required VoidCallback? onPressed,
    required IconData icon,
    required String label,
    required Color backgroundColor,
    required Color foregroundColor,
    bool isLoading = false,
  }) {
    return Container(
      height: UIConstants.buttonHeightL,
      constraints: BoxConstraints(
        minWidth: 120.w,
      ),
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor,
          elevation: 0,
          shadowColor: Colors.transparent,
          disabledBackgroundColor: backgroundColor.withOpacity(0.6),
          disabledForegroundColor: foregroundColor.withOpacity(0.8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignTokens.radiusM),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: DesignTokens.spacingL,
            vertical: UIConstants.spacingM,
          ),
        ),
        child: isLoading
            ? SizedBox(
                width: 20.w,
                height: 20.h,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(foregroundColor),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(icon, size: UIConstants.iconS),
                  SizedBox(width: UIConstants.spacingXS.w),
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeS,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildCommandFeedback(BuildContext context) {
    final commandState = ref.watch(trackingCommandStateProvider);
    final connectionStatus = ref.watch(connectionStatusProvider);

    if (commandState != null) {
      return Container(
        padding: EdgeInsets.symmetric(
          horizontal: UIConstants.spacingM.w,
          vertical: UIConstants.spacingS.h,
        ),
        decoration: BoxDecoration(
          color: AppTheme.successColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(DesignTokens.radiusS),
          border: Border.all(
            color: AppTheme.successColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 12.r,
              height: 12.r,
              child: CircularProgressIndicator(
                strokeWidth: 1.5,
                valueColor: AlwaysStoppedAnimation<Color>(AppTheme.successColor),
              ),
            ),
            SizedBox(width: UIConstants.spacingS.w),
            Text(
              'Executing ${commandState} command...',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.successColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    // Show connection quality info
    if (connectionStatus.isConnected) {
      String responseInfo = connectionStatus.connectionType == ConnectionType.http
          ? 'Response time: ~1s'
          : 'Real-time updates';

      return Text(
        responseInfo,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Theme.of(context).colorScheme.onSurfaceVariant,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
