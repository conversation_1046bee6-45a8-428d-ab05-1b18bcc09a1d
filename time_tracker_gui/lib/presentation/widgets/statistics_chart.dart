import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../data/models/app_model.dart';
import '../../app/design_system/design_system.dart';

class StatisticsChart extends StatefulWidget {
  final List<AppStatistics> statistics;

  const StatisticsChart({
    super.key,
    required this.statistics,
  });

  @override
  State<StatisticsChart> createState() => _StatisticsChartState();
}

class _StatisticsChartState extends State<StatisticsChart> {
  int _selectedChartType = 0; // 0: Bar Chart, 1: Pie Chart, 2: Line Chart

  // Helper methods for ultra-small device detection
  bool _isUltraSmall(BuildContext context) => MediaQuery.of(context).size.width < 360;
  bool _isSmallMobile(BuildContext context) => MediaQuery.of(context).size.width < 480;
  bool _isCompactHeight(BuildContext context) => MediaQuery.of(context).size.height < 600;

  @override
  Widget build(BuildContext context) {
    final isMobile = context.isMobile;
    final isUltraSmall = _isUltraSmall(context);
    final isSmallMobile = _isSmallMobile(context);
    final isCompactHeight = _isCompactHeight(context);

    if (widget.statistics.isEmpty) {
      return ModernContainer(
        padding: EdgeInsets.all(
          isUltraSmall ? DesignTokens.spacingS :
          (isMobile ? DesignTokens.spacingM : DesignTokens.spacingXL)
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics_outlined,
              size: isUltraSmall ? DesignTokens.iconM :
                   (isMobile ? DesignTokens.iconL : DesignTokens.iconXL),
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: isUltraSmall ? DesignTokens.spacingS :
                           (isMobile ? DesignTokens.spacingM : DesignTokens.spacingL)),
            Text(
              'No statistics available',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontSize: isUltraSmall ? DesignTokens.fontSize12 :
                         (isMobile ? DesignTokens.fontSize14 : null),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: isUltraSmall ? 2 :
                           (isMobile ? DesignTokens.spacingXS : DesignTokens.spacingS)),
            Text(
              isUltraSmall ? 'Start tracking to see charts' :
              'Start tracking your apps to see beautiful charts here',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                fontSize: isUltraSmall ? DesignTokens.fontSize10 :
                         (isMobile ? DesignTokens.fontSize12 : null),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ResponsiveContainer(
      maxWidth: DesignTokens.maxContentWidth,
      padding: EdgeInsets.all(
        isUltraSmall ? DesignTokens.spacingXS : context.contentPadding
      ),
      child: ModernColumn(
        spacing: isUltraSmall ? DesignTokens.spacingXS :
                (isMobile ? DesignTokens.spacingS : DesignTokens.spacingM),
        children: [
          if (!isCompactHeight) _buildChartTypeSelector(),
          Expanded(
            child: _buildSelectedChart(),
          ),
          if (isCompactHeight) _buildCompactChartTypeSelector(),
        ],
      ),
    );
  }

  Widget _buildChartTypeSelector() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = context.isMobile;
        final isUltraSmall = _isUltraSmall(context);
        final showLabels = constraints.maxWidth > (isUltraSmall ? 300 : 400);

        return ModernContainer(
          padding: EdgeInsets.all(
            isUltraSmall ? 2 : (isMobile ? DesignTokens.spacingXS : DesignTokens.spacingS)
          ),
          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(isUltraSmall ? DesignTokens.radiusS : DesignTokens.radiusL),
          child: SegmentedButton<int>(
            segments: [
              ButtonSegment(
                value: 0,
                label: showLabels ? const Text('Bar') : null,
                icon: Icon(
                  Icons.bar_chart_outlined,
                  size: isUltraSmall ? DesignTokens.iconXS : DesignTokens.iconS,
                ),
              ),
              ButtonSegment(
                value: 1,
                label: showLabels ? const Text('Pie') : null,
                icon: Icon(
                  Icons.pie_chart,
                  size: isUltraSmall ? DesignTokens.iconXS : DesignTokens.iconS,
                ),
              ),
              ButtonSegment(
                value: 2,
                label: showLabels ? const Text('Line') : null,
                icon: Icon(
                  Icons.show_chart,
                  size: isUltraSmall ? DesignTokens.iconXS : DesignTokens.iconS,
                ),
              ),
            ],
            selected: {_selectedChartType},
            onSelectionChanged: (Set<int> selection) {
              setState(() {
                _selectedChartType = selection.first;
              });
            },
            style: SegmentedButton.styleFrom(
              selectedBackgroundColor: AppColors.primary500,
              selectedForegroundColor: AppColors.neutral0,
              backgroundColor: Colors.transparent,
              side: BorderSide.none,
              minimumSize: Size(
                isUltraSmall ? 32 : (isMobile ? 40 : 60),
                isUltraSmall ? 28 : (isMobile ? 36 : 40)
              ),
              textStyle: TextStyle(
                fontSize: isUltraSmall ? DesignTokens.fontSize10 : DesignTokens.fontSize12,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCompactChartTypeSelector() {
    return SizedBox(
      height: 40,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildCompactChartButton(0, Icons.bar_chart_outlined, 'Bar'),
          _buildCompactChartButton(1, Icons.pie_chart, 'Pie'),
          _buildCompactChartButton(2, Icons.show_chart, 'Line'),
        ],
      ),
    );
  }

  Widget _buildCompactChartButton(int value, IconData icon, String label) {
    final isSelected = _selectedChartType == value;
    final isUltraSmall = _isUltraSmall(context);

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedChartType = value;
          });
        },
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: isUltraSmall ? 1 : 2),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary500 : Colors.transparent,
            borderRadius: BorderRadius.circular(DesignTokens.radiusS),
            border: Border.all(
              color: isSelected ? AppColors.primary500 : AppColors.neutral400,
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: isUltraSmall ? DesignTokens.iconXS : DesignTokens.iconS,
                color: isSelected ? AppColors.neutral0 : Theme.of(context).colorScheme.onSurface,
              ),
              if (!isUltraSmall) ...[
                const SizedBox(height: 2),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: DesignTokens.fontSize10,
                    color: isSelected ? AppColors.neutral0 : Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedChart() {
    switch (_selectedChartType) {
      case 0:
        return _buildBarChart();
      case 1:
        return _buildPieChart();
      case 2:
        return _buildLineChart();
      default:
        return _buildBarChart();
    }
  }

  Widget _buildBarChart() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = context.isMobile;
        final isTablet = context.isTablet;
        final isUltraSmall = _isUltraSmall(context);
        final isSmallMobile = _isSmallMobile(context);

        final sortedStats = List<AppStatistics>.from(widget.statistics)
          ..sort((a, b) => b.totalDuration.compareTo(a.totalDuration));

        // Show fewer apps on smaller screens
        final maxApps = isUltraSmall ? 4 :
                       (isSmallMobile ? 5 :
                       (isMobile ? 6 : (isTablet ? 8 : 10)));
        final topApps = sortedStats.take(maxApps).toList();

        return ModernContainer(
          padding: EdgeInsets.all(
            isUltraSmall ? DesignTokens.spacingXS :
            (isMobile ? DesignTokens.spacingS : DesignTokens.spacingM)
          ),
          child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: topApps.isNotEmpty ? topApps.first.totalDuration.toDouble() * 1.2 : 100,
          barTouchData: BarTouchData(
            touchTooltipData: BarTouchTooltipData(
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                final app = topApps[group.x];
                return BarTooltipItem(
                  '${app.app.name}\n${_formatDuration(app.totalDuration)}',
                  const TextStyle(
                    color: AppColors.neutral0,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: !isUltraSmall, // Hide titles on ultra-small screens
                reservedSize: isUltraSmall ? 20 : (isSmallMobile ? 30 : 40),
                getTitlesWidget: (value, meta) {
                  if (value.toInt() < topApps.length) {
                    final app = topApps[value.toInt()];
                    final maxLength = isUltraSmall ? 4 : (isSmallMobile ? 6 : 8);
                    final appName = app.app.name ?? 'Unknown';

                    return Padding(
                      padding: EdgeInsets.only(
                        top: isUltraSmall ? 2 : DesignTokens.spacingXS
                      ),
                      child: Text(
                        appName.length > maxLength
                            ? '${appName.substring(0, maxLength)}...'
                            : appName,
                        style: TextStyle(
                          fontSize: isUltraSmall ? 8 : (isSmallMobile ? 9 : 10),
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: !isUltraSmall, // Hide on ultra-small screens to save space
                reservedSize: isUltraSmall ? 0 : (isSmallMobile ? 35 : 50),
                getTitlesWidget: (value, meta) {
                  return Text(
                    _formatDurationShort(value.toInt()),
                    style: TextStyle(
                      fontSize: isUltraSmall ? 8 : (isSmallMobile ? 9 : 10),
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false),
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            horizontalInterval: topApps.isNotEmpty ? topApps.first.totalDuration.toDouble() / 5 : 20,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                strokeWidth: 1,
              );
            },
          ),
          barGroups: topApps.asMap().entries.map((entry) {
            return BarChartGroupData(
              x: entry.key,
              barRods: [
                BarChartRodData(
                  toY: entry.value.totalDuration.toDouble(),
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary500,
                      AppColors.primary400,
                    ],
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                  ),
                  width: isUltraSmall ? 12 : (isSmallMobile ? 16 : 20),
                  borderRadius: BorderRadius.circular(
                    isUltraSmall ? DesignTokens.radiusXS : DesignTokens.radiusS
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
      },
    );
  }

  Widget _buildPieChart() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = context.isMobile;
        final isTablet = context.isTablet;
        final isUltraSmall = _isUltraSmall(context);
        final isSmallMobile = _isSmallMobile(context);

        final sortedStats = List<AppStatistics>.from(widget.statistics)
          ..sort((a, b) => b.totalDuration.compareTo(a.totalDuration));

        // Show fewer apps on smaller screens
        final maxApps = isUltraSmall ? 3 :
                       (isSmallMobile ? 4 :
                       (isMobile ? 5 : (isTablet ? 6 : 8)));
        final topApps = sortedStats.take(maxApps).toList();
        final colors = [
          AppColors.primary500,
          AppColors.secondary500,
          AppColors.tertiary500,
          AppColors.warning,
          AppColors.success,
          AppColors.steamPurple,
          AppColors.error,
          AppColors.info,
        ];

        return ModernContainer(
          padding: EdgeInsets.all(
            isUltraSmall ? DesignTokens.spacingXS :
            (isMobile ? DesignTokens.spacingS : DesignTokens.spacingL)
          ),
          child: isMobile ? _buildMobilePieChart(topApps, colors, context) : _buildDesktopPieChart(topApps, colors, context),
        );
      },
    );
  }

  Widget _buildMobilePieChart(List<AppStatistics> topApps, List<Color> colors, BuildContext context) {
    final isUltraSmall = _isUltraSmall(context);
    final isSmallMobile = _isSmallMobile(context);

    return ModernColumn(
      spacing: isUltraSmall ? DesignTokens.spacingS : DesignTokens.spacingM,
      children: [
        SizedBox(
          height: isUltraSmall ? 140 : (isSmallMobile ? 170 : 200),
          child: PieChart(
            PieChartData(
              sections: topApps.asMap().entries.map((entry) {
                final index = entry.key;
                final stat = entry.value;
                final percentage = ((stat.totalDuration / widget.statistics.fold(0, (sum, s) => sum + s.totalDuration)) * 100);

                return PieChartSectionData(
                  color: colors[index % colors.length],
                  value: stat.totalDuration.toDouble(),
                  title: isUltraSmall ? '${percentage.toStringAsFixed(0)}%' : '${percentage.toStringAsFixed(1)}%',
                  radius: isUltraSmall ? 45 : (isSmallMobile ? 55 : 60),
                  titleStyle: TextStyle(
                    fontSize: isUltraSmall ? 8 : (isSmallMobile ? 9 : 10),
                    fontWeight: FontWeight.bold,
                    color: AppColors.neutral0,
                  ),
                );
              }).toList(),
              centerSpaceRadius: isUltraSmall ? 25 : (isSmallMobile ? 35 : 40),
              sectionsSpace: isUltraSmall ? 1 : 2,
            ),
          ),
        ),
        _buildPieChartLegend(topApps, colors, context, true),
      ],
    );
  }

  Widget _buildDesktopPieChart(List<AppStatistics> topApps, List<Color> colors, BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: PieChart(
            PieChartData(
              sections: topApps.asMap().entries.map((entry) {
                final index = entry.key;
                final stat = entry.value;
                return PieChartSectionData(
                  color: colors[index % colors.length],
                  value: stat.totalDuration.toDouble(),
                  title: '${((stat.totalDuration / widget.statistics.fold(0, (sum, s) => sum + s.totalDuration)) * 100).toStringAsFixed(1)}%',
                  radius: 80,
                  titleStyle: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.neutral0,
                  ),
                );
              }).toList(),
              centerSpaceRadius: 50,
              sectionsSpace: 2,
            ),
          ),
        ),
        const SizedBox(width: DesignTokens.spacingL),
        Expanded(
          child: _buildPieChartLegend(topApps, colors, context, false),
        ),
      ],
    );
  }

  Widget _buildPieChartLegend(List<AppStatistics> topApps, List<Color> colors, BuildContext context, bool isMobile) {
    return ModernColumn(
      spacing: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingS,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: topApps.asMap().entries.map((entry) {
        final index = entry.key;
        final stat = entry.value;
        final percentage = ((stat.totalDuration / widget.statistics.fold(0, (sum, s) => sum + s.totalDuration)) * 100);

        return Row(
          children: [
            Container(
              width: isMobile ? 10 : 12,
              height: isMobile ? 10 : 12,
              decoration: BoxDecoration(
                color: colors[index % colors.length],
                borderRadius: BorderRadius.circular(DesignTokens.radiusS),
              ),
            ),
            SizedBox(width: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingS),
            Expanded(
              child: Text(
                stat.app.name ?? 'Unknown',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: isMobile ? DesignTokens.fontSize10 : DesignTokens.fontSize12,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Text(
              '${percentage.toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: colors[index % colors.length],
                fontSize: isMobile ? DesignTokens.fontSize10 : DesignTokens.fontSize12,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildLineChart() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = context.isMobile;

        return ModernContainer(
          padding: EdgeInsets.all(isMobile ? DesignTokens.spacingS : DesignTokens.spacingM),
          child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            horizontalInterval: 30,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                strokeWidth: 1,
              );
            },
          ),
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                getTitlesWidget: (value, meta) {
                  return Text(
                    'Day ${value.toInt() + 1}',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  );
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 50,
                getTitlesWidget: (value, meta) {
                  return Text(
                    _formatDurationShort(value.toInt()),
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          lineBarsData: [
            LineChartBarData(
              spots: List.generate(7, (index) {
                // Mock data for the last 7 days
                final value = (widget.statistics.isNotEmpty
                    ? widget.statistics.first.todayDuration
                    : 0) * (0.5 + (index * 0.1));
                return FlSpot(index.toDouble(), value.toDouble());
              }),
              isCurved: true,
              gradient: LinearGradient(
                colors: [AppColors.primary500, AppColors.primary400],
              ),
              barWidth: 3,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(
                    radius: 4,
                    color: AppColors.primary500,
                    strokeWidth: 2,
                    strokeColor: AppColors.neutral0,
                  );
                },
              ),
              belowBarData: BarAreaData(
                show: true,
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary500.withValues(alpha: 0.3),
                    AppColors.primary500.withValues(alpha: 0.1),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
          ],
          lineTouchData: LineTouchData(
            touchTooltipData: LineTouchTooltipData(
              getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                return touchedBarSpots.map((barSpot) {
                  return LineTooltipItem(
                    'Day ${barSpot.x.toInt() + 1}\n${_formatDuration(barSpot.y.toInt())}',
                    const TextStyle(
                      color: AppColors.neutral0,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  );
                }).toList();
              },
            ),
          ),
        ),
      ),
    );
      },
    );
  }



  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '${minutes}m';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      return '${hours}h ${remainingMinutes}m';
    }
  }

  String _formatDurationShort(int minutes) {
    if (minutes < 60) {
      return '${minutes}m';
    } else {
      final hours = minutes ~/ 60;
      return '${hours}h';
    }
  }
}
