import 'package:meta/meta.dart';

/// Timeline filter types for different time periods
enum TimelineFilterType {
  all,
  today,
  yesterday,
  thisWeek,
  lastWeek,
  thisMonth,
  lastMonth,
  custom,
}

/// Timeline sorting options
enum TimelineSortBy {
  dateNewest,
  dateOldest,
  durationLongest,
  durationShortest,
  appName,
}

/// Helper class to distinguish between null and undefined values in copyWith
@internal
class _Undefined {
  const _Undefined();
}