import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'timeline_enums.dart';
import 'timeline_internal.dart';

/// State class for timeline filtering and sorting
@immutable
class TimelineFilterState {
  final TimelineFilterType filterType;
  final TimelineSortBy sortBy;
  final String searchQuery;
  final DateTime? customStartDate;
  final DateTime? customEndDate;
  final int? selectedAppId;
  final bool showAll;

  const TimelineFilterState({
    this.filterType = TimelineFilterType.all,
    this.sortBy = TimelineSortBy.dateNewest,
    this.searchQuery = '',
    this.customStartDate,
    this.customEndDate,
    this.selectedAppId,
    this.showAll = false,
  });

  TimelineFilterState copyWith({
    TimelineFilterType? filterType,
    TimelineSortBy? sortBy,
    String? searchQuery,
    Object? customStartDate = const Undefined(),
    Object? customEndDate = const Undefined(),
    Object? selectedAppId = const Undefined(),
    bool? showAll,
  }) {
    return TimelineFilterState(
      filterType: filterType ?? this.filterType,
      sortBy: sortBy ?? this.sortBy,
      searchQuery: searchQuery ?? this.searchQuery,
      customStartDate: customStartDate is Undefined
          ? this.customStartDate
          : customStartDate as DateTime?,
      customEndDate: customEndDate is Undefined
          ? this.customEndDate
          : customEndDate as DateTime?,
      selectedAppId: selectedAppId is Undefined
          ? this.selectedAppId
          : selectedAppId as int?,
      showAll: showAll ?? this.showAll,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'filterType': filterType.index,
      'sortBy': sortBy.index,
      'searchQuery': searchQuery,
      'customStartDate': customStartDate?.toIso8601String(),
      'customEndDate': customEndDate?.toIso8601String(),
      'selectedAppId': selectedAppId,
      'showAll': showAll,
    };
  }

  factory TimelineFilterState.fromJson(Map<String, dynamic> json) {
    return TimelineFilterState(
      filterType:
          TimelineFilterType.values.elementAtOrNull(json['filterType'] ?? 0) ??
              TimelineFilterType.all,
      sortBy: TimelineSortBy.values.elementAtOrNull(json['sortBy'] ?? 0) ??
          TimelineSortBy.dateNewest,
      searchQuery: json['searchQuery'] ?? '',
      customStartDate: json['customStartDate'] != null
          ? DateTime.tryParse(json['customStartDate'])
          : null,
      customEndDate: json['customEndDate'] != null
          ? DateTime.tryParse(json['customEndDate'])
          : null,
      selectedAppId: json['selectedAppId'],
      showAll: json['showAll'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimelineFilterState &&
        other.filterType == filterType &&
        other.sortBy == sortBy &&
        other.searchQuery == searchQuery &&
        other.customStartDate == customStartDate &&
        other.customEndDate == customEndDate &&
        other.selectedAppId == selectedAppId &&
        other.showAll == showAll;
  }

  @override
  int get hashCode {
    return Object.hash(
      filterType,
      sortBy,
      searchQuery,
      customStartDate,
      customEndDate,
      selectedAppId,
      showAll,
    );
  }
}