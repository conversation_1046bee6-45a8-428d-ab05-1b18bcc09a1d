import 'package:flutter/foundation.dart';
import 'package:meta/meta.dart';
import '../../../data/models/app_model.dart';

/// Data structure to represent either a single timeline entry or a group of consecutive identical entries
@immutable
sealed class TimelineEntry {
  const TimelineEntry();
}

/// Represents a single timeline entry
class SingleTimelineEntry extends TimelineEntry {
  final TimelineModel session;

  const SingleTimelineEntry(this.session);
}

/// Represents a group of consecutive identical timeline entries
class GroupedTimelineEntry extends TimelineEntry {
  final List<TimelineModel> sessions;
  final AppModel app;
  final bool isCollapsed;

  const GroupedTimelineEntry({
    required this.sessions,
    required this.app,
    this.isCollapsed = true,
  });

  /// Get the earliest date in the group
  DateTime? get startDate {
    if (sessions.isEmpty) return null;
    return sessions.map((s) => s.date).whereType<DateTime>().reduce(
          (a, b) => a.isBefore(b) ? a : b,
        );
  }

  /// Get the latest date in the group
  DateTime? get endDate {
    if (sessions.isEmpty) return null;
    return sessions.map((s) => s.date).whereType<DateTime>().reduce(
          (a, b) => a.isAfter(b) ? a : b,
        );
  }

  /// Get the total duration of all sessions in the group
  int get totalDuration {
    return sessions.fold(0, (sum, session) => sum + (session.duration ?? 0));
  }
}