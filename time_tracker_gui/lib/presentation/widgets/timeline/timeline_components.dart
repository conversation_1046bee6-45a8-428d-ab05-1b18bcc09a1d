import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:time_tracker_gui/util/time_util.dart';
import '../../../core/constants/ui_constants.dart';
import '../../../app/theme/app_theme.dart';
import '../../../app/design_system/design_system.dart';
import '../../../data/models/app_model.dart';
import '../../../data/models/poster_model.dart';
import '../../providers/local_game_poster_providers.dart';
import '../../providers/timeline_filter_providers.dart';
import '../common_ui_components.dart';
import '../dialog_components.dart';
import '../apps_page_components.dart';
import 'timeline_filter_notifier.dart';
import 'timeline_entries.dart';
import 'timeline_utils.dart';
import 'timeline_filter_state.dart';
import 'timeline_enums.dart';
import 'timeline_search_field.dart';
import 'timeline_shared_utils.dart';
import 'timeline_header.dart';

class TimelineComponents extends ConsumerWidget {
  const TimelineComponents({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filteredTimeline = ref.watch(filteredTimelineProvider);
    final appMap = ref.watch(appLookupMapProvider);
    final filterState = ref.watch(timelineFilterProvider);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return filteredTimeline.when(
      data: (timelineData) => appMap.when(
        data: (appLookupMap) {
          return _buildTimelineList(timelineData, appLookupMap, isDark, theme, ref, filterState, context);
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, _) => CommonUIComponents.buildErrorState('Failed to load apps'),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) => CommonUIComponents.buildErrorState('Failed to load timeline'),
    );
  }

  Widget _buildTimelineList(
    List<TimelineModel> timelineData,
    Map<int, AppModel> appMap,
    bool isDark,
    ThemeData theme,
    WidgetRef ref,
    TimelineFilterState filterState,
    BuildContext context,
  ) {
    if (timelineData.isEmpty) {
      return CommonUIComponents.buildGamingEmptyState(
        'No gaming sessions found',
        'Try adjusting your filters or start playing to see your timeline here',
        isDark,
        theme,
      );
    }

    // Group consecutive identical timeline entries
    final groupedEntries = groupTimelineEntries(timelineData, appMap);

    // Flatten the entries based on their expansion state
    final flattenedEntries = <Object>[];
    final expansionState = ref.watch(timelineGroupExpansionProvider);

    for (final entry in groupedEntries) {
      if (entry is SingleTimelineEntry) {
        flattenedEntries.add(entry);
      } else if (entry is GroupedTimelineEntry) {
        // Create a unique key for this group
        final groupKey = entry.sessions.isNotEmpty
            ? '${entry.sessions.first.id}-${entry.sessions.last.id}'
            : 'empty-group';

        // Always add the group header
        flattenedEntries.add(entry);

        // Check if this group is expanded
        final isExpanded = expansionState[groupKey] ?? false;

        if (isExpanded) {
          // Add individual sessions when expanded
          for (final session in entry.sessions) {
            flattenedEntries.add(session);
          }
        }
      }
    }

    const int initialItemCount = 20; // Increased for better UX
    final int itemCount = filterState.showAll ? flattenedEntries.length :
        (flattenedEntries.length > initialItemCount ? initialItemCount : flattenedEntries.length);
    final bool hasMoreItems = flattenedEntries.length > initialItemCount && !filterState.showAll;

    // Use ListView.builder for better performance with large lists
    final isMobile = MediaQuery.of(context).size.width < DesignTokens.breakpointMobile;
    
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: DesignTokens.spacingM),
      itemCount: itemCount + (hasMoreItems ? 1 : 0) + (!filterState.showAll && flattenedEntries.length > initialItemCount && filterState.showAll ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < itemCount) {
          final entry = flattenedEntries[index];
          return switch (entry) {
            SingleTimelineEntry(:final session) =>
              _buildTimelineItem(session, appMap[session.appId] ?? AppModel(id: session.appId, name: 'Unknown Game'), isDark, theme, ref, isMobile),
            GroupedTimelineEntry() =>
              _buildGroupedTimelineItemWithExpansion(entry, isDark, theme, ref),
            TimelineModel(:final appId) =>
              // This is an individual session from an expanded group
              _buildTimelineItem(entry, appMap[appId] ?? AppModel(id: appId, name: 'Unknown Game'), isDark, theme, ref, isMobile),
            _ => const SizedBox.shrink(),
          };
        } else if (hasMoreItems && index == itemCount) {
          return _buildShowMoreButton(ref, theme, isDark, isMobile);
        } else if (filterState.showAll && flattenedEntries.length > initialItemCount && index == itemCount) {
          return _buildShowLessButton(ref, theme, isDark, isMobile);
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildShowMoreButton(WidgetRef ref, ThemeData theme, bool isDark, bool isMobile) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: DesignTokens.spacingM),
      child: Center(
        child: ElevatedButton.icon(
          onPressed: () => ref.read(timelineFilterProvider.notifier).toggleShowAll(),
          style: ElevatedButton.styleFrom(
            backgroundColor: isDark ? theme.cardBg : theme.colorScheme.surface,
            foregroundColor: theme.colorScheme.primary,
            elevation: 2,
            padding: EdgeInsets.symmetric(
              horizontal: isMobile ? DesignTokens.spacingS : DesignTokens.spacingL,
              vertical: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM,
            ),
          ),
          icon: const Icon(Icons.expand_more),
          label: const Text(
            'Show more sessions',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildShowLessButton(WidgetRef ref, ThemeData theme, bool isDark, bool isMobile) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: DesignTokens.spacingM),
      child: Center(
        child: ElevatedButton.icon(
          onPressed: () => ref.read(timelineFilterProvider.notifier).toggleShowAll(),
          style: ElevatedButton.styleFrom(
            backgroundColor: isDark ? theme.cardBg : theme.colorScheme.surface,
            foregroundColor: theme.colorScheme.primary,
            elevation: 2,
            padding: EdgeInsets.symmetric(
              horizontal: isMobile ? DesignTokens.spacingS : DesignTokens.spacingL,
              vertical: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM,
            ),
          ),
          icon: const Icon(Icons.expand_less),
          label: const Text(
            'Show less',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTimelineItem(TimelineModel session, AppModel app, bool isDark, ThemeData theme, WidgetRef ref, bool isMobile) {
    final backgroundColor = const Color(0xFF111714);
    final gameName = _getGameName(app);
    final posterAsync = ref.watch(cachedPosterProvider(gameName));
    final imagePathAsync = ref.watch(cachedImagePathProvider(gameName));

    return Container(
      margin: EdgeInsets.only(
        bottom: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingM,
        top: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingS,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? const Color(0xFF2A3A32) : const Color(0xFF3D5248),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withOpacity(0.3)
                : Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Circular app image with ring
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
                    border: Border.all(
                      color: const Color(0x1AFFFFFF),
                      width: 2,
                    ),
                  ),
                  child: ClipOval(
                    child: _buildPosterImage(ref, gameName, posterAsync, imagePathAsync, isDark),
                  ),
                ),
                const SizedBox(width: 16),
                // Game name and date
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        app.name ?? 'Unknown Game',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          height: 1.5,
                        ),
                      ),
                      Text(
                        _formatDate(session.date),
                        style: const TextStyle(
                          color: Color(0xFF9EB7A8),
                          fontSize: 14,
                          fontWeight: FontWeight.normal,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),
                // Play time
                Text(
                  TimeUtil.formatDurationFromMinutesAsHours(session.duration ?? 0),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.normal,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGroupedTimelineItem(
    GroupedTimelineEntry groupedEntry,
    bool isDark,
    ThemeData theme,
    WidgetRef ref,
    void Function() onToggle,
  ) {
    final backgroundColor = const Color(0xFF111714);
    final gameName = _getGameName(groupedEntry.app);
    final posterAsync = ref.watch(cachedPosterProvider(gameName));
    final imagePathAsync = ref.watch(cachedImagePathProvider(gameName));

    return Container(
      margin: EdgeInsets.only(bottom: DesignTokens.spacingM, top: DesignTokens.spacingS),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? const Color(0xFF2A3A32) : const Color(0xFF3D5248),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withOpacity(0.3)
                : Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onToggle,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Circular app image with ring
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
                    border: Border.all(
                      color: const Color(0x1AFFFFFF),
                      width: 2,
                    ),
                  ),
                  child: ClipOval(
                    child: _buildPosterImage(ref, gameName, posterAsync, imagePathAsync, isDark),
                  ),
                ),
                const SizedBox(width: 16),
                // Game name and date range
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        groupedEntry.app.name ?? 'Unknown Game',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          height: 1.5,
                        ),
                      ),
                      Text(
                        _formatDateRange(groupedEntry.startDate, groupedEntry.endDate),
                        style: const TextStyle(
                          color: Color(0xFF9EB7A8),
                          fontSize: 14,
                          fontWeight: FontWeight.normal,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),
                // Total play time and expand/collapse icon
                Row(
                  children: [
                    Text(
                      TimeUtil.formatDurationFromMinutesAsHours(groupedEntry.totalDuration),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.normal,
                        height: 1.5,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      groupedEntry.isCollapsed ? Icons.expand_more : Icons.expand_less,
                      color: const Color(0xFF9EB7A8),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGroupedTimelineItemWithExpansion(
    GroupedTimelineEntry groupedEntry,
    bool isDark,
    ThemeData theme,
    WidgetRef ref,
  ) {
    // Create a unique key for this group based on the first and last session IDs
    final groupKey = groupedEntry.sessions.isNotEmpty
        ? '${groupedEntry.sessions.first.id}-${groupedEntry.sessions.last.id}'
        : 'empty-group';

    final expansionState = ref.watch(timelineGroupExpansionProvider);
    final isExpanded = expansionState[groupKey] ?? false;

    // Create a modified entry with the current expansion state
    final entryWithState = GroupedTimelineEntry(
      sessions: groupedEntry.sessions,
      app: groupedEntry.app,
      isCollapsed: !isExpanded,
    );

    return _buildGroupedTimelineItem(
      entryWithState,
      isDark,
      theme,
      ref,
      () {
        // Toggle the expansion state
        final currentState = ref.read(timelineGroupExpansionProvider);
        final newState = Map<String, bool>.from(currentState);
        newState[groupKey] = !(newState[groupKey] ?? false);
        ref.read(timelineGroupExpansionProvider.notifier).state = newState;
      },
    );
  }

  String _formatDateRange(DateTime? startDate, DateTime? endDate) {
    if (startDate == null || endDate == null) return 'Unknown date range';

    // If start and end dates are the same day
    if (startDate.year == endDate.year &&
        startDate.month == endDate.month &&
        startDate.day == endDate.day) {
      return '${_formatDate(startDate)} - ${_formatTime(endDate)}';
    }

    // Different days
    return '${_formatDate(startDate)} - ${_formatDate(endDate)}';
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown date';

    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today at ${_formatTime(date)}';
    } else if (difference == 1) {
      return 'Yesterday at ${_formatTime(date)}';
    } else if (difference < 7) {
      return '${difference}d ago at ${_formatTime(date)}';
    } else {
      return '${date.day}/${date.month}/${date.year} at ${_formatTime(date)}';
    }
  }

  String _formatTime(DateTime date) {
    final hour = date.hour.toString().padLeft(2, '0');
    final minute = date.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  String _getGameName(AppModel app) {
    String gameName = '';
    if (app.productName?.isNotEmpty == true) {
      gameName = app.productName!;
    } else if (app.name?.isNotEmpty == true) {
      gameName = app.name!;
    }

    // Clean up the game name for better search results
    gameName = gameName
        .replaceAll(RegExp(r'\.exe$', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();

    return gameName;
  }

  Widget _buildPosterImage(
    WidgetRef ref,
    String gameName,
    AsyncValue<Poster?> posterAsync,
    AsyncValue<String?> imagePathAsync,
    bool isDark,
  ) {
    return posterAsync.when(
      data: (poster) {
        if (poster != null && poster.hasImage) {
          return imagePathAsync.when(
            data: (imagePath) {
              if (imagePath != null) {
                // Use cached local image
                return Image.file(
                  File(imagePath),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // Fallback to network image if local file fails
                    return _buildNetworkImage(poster.backgroundImage, isDark);
                  },
                );
              } else {
                // Use network image
                return _buildNetworkImage(poster.backgroundImage, isDark);
              }
            },
            loading: () => _buildNetworkImage(poster.backgroundImage, isDark),
            error: (error, stackTrace) => _buildNetworkImage(poster.backgroundImage, isDark),
          );
        } else {
          return _buildGamePlaceholder(isDark);
        }
      },
      loading: () => _buildLoadingPlaceholder(isDark),
      error: (error, stackTrace) => _buildGamePlaceholder(isDark),
    );
  }

  Widget _buildNetworkImage(String imageUrl, bool isDark) {
    return Image.network(
      imageUrl,
      fit: BoxFit.cover,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return _buildLoadingPlaceholder(isDark);
      },
      errorBuilder: (context, error, stackTrace) {
        return _buildGamePlaceholder(isDark);
      },
    );
  }

  Widget _buildLoadingPlaceholder(bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildGamePlaceholder(bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
      ),
      child: const Center(
        child: Icon(
          Icons.games,
          color: Colors.white70,
        ),
      ),
    );
  }
}
