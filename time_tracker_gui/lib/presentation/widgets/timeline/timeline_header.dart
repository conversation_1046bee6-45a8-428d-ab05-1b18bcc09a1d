import '../common_ui_components.dart';
import '../dialog_components.dart';
import '../apps_page_components.dart';
import '../common/search_filter_layout.dart';
import 'timeline_filter_notifier.dart';
import 'timeline_entries.dart';
import 'timeline_utils.dart';
import 'timeline_filter_state.dart';
import 'timeline_enums.dart';
import 'timeline_search_field.dart';
import 'timeline_shared_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../app/design_system/design_system.dart';
import '../../../core/constants/ui_constants.dart';

class TimelineHeader extends ConsumerWidget {
  const TimelineHeader({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filterState = ref.watch(timelineFilterProvider);
    final hasActiveFilters = filterState.searchQuery.isNotEmpty ||
                           filterState.filterType != TimelineFilterType.all ||
                           filterState.sortBy != TimelineSortBy.dateNewest ||
                           filterState.selectedAppId != null;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return SearchFilterLayout(
      searchWidget: TimelineSearchField(),
      onFilterPressed: () => DialogComponents.showTimelineFilterDialog(context, ref),
      onClearPressed: hasActiveFilters ? () => ref.read(timelineFilterProvider.notifier).clearFilters() : null,
      hasActiveFilters: hasActiveFilters,
      filterChips: hasActiveFilters ? Wrap(
        spacing: DesignTokens.spacingM,
        children: [
          if (filterState.searchQuery.isNotEmpty)
            AppsPageComponents.buildFilterChip(
              'Search: "${filterState.searchQuery}"',
              () => ref.read(timelineFilterProvider.notifier).updateSearchQuery(''),
              isDark,
              theme,
            ),
          if (filterState.filterType != TimelineFilterType.all)
            AppsPageComponents.buildFilterChip(
              'Period: ${getTimelineFilterTypeLabel(filterState.filterType)}',
              () => ref.read(timelineFilterProvider.notifier).updateFilterType(TimelineFilterType.all),
              isDark,
              theme,
            ),
          if (filterState.sortBy != TimelineSortBy.dateNewest)
            AppsPageComponents.buildFilterChip(
              'Sort: ${getTimelineSortByLabel(filterState.sortBy)}',
              () => ref.read(timelineFilterProvider.notifier).updateSortBy(TimelineSortBy.dateNewest),
              isDark,
              theme,
            ),
          if (filterState.selectedAppId != null)
            AppsPageComponents.buildFilterChip(
              'Game Filter Active',
              () => ref.read(timelineFilterProvider.notifier).updateSelectedApp(null),
              isDark,
              theme,
            ),
        ],
      ) : null,
    );
  }
}
