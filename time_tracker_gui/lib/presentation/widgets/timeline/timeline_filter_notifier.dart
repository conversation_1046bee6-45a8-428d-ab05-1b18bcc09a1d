import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_riverpod/legacy.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'timeline_filter_state.dart';
import 'timeline_enums.dart';

/// Provider for timeline filter state management
final timelineFilterProvider =
    StateNotifierProvider<TimelineFilterNotifier, TimelineFilterState>(
  (ref) => TimelineFilterNotifier(),
);

/// Provider for managing the expanded/collapsed state of timeline groups
final timelineGroupExpansionProvider =
    StateProvider<Map<String, bool>>((ref) => {});

/// Notifier for managing timeline filter state
class TimelineFilterNotifier extends StateNotifier<TimelineFilterState> {
  static const String _timelineFilterKey = 'timeline_filter_state';
  Timer? _debounceTimer;
  Timer? _saveTimer;

  TimelineFilterNotifier() : super(const TimelineFilterState()) {
    _loadFilterState();
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _saveTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadFilterState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final filterJson = prefs.getString(_timelineFilterKey);

      if (filterJson != null) {
        final Map<String, dynamic> json = jsonDecode(filterJson);
        state = TimelineFilterState.fromJson(json);
      }
    } catch (e) {
      // If loading fails, keep default state
      // Using debugPrint instead of print for better performance in release builds
      debugPrint('Failed to load timeline filter state: $e');
    }
  }

  Future<void> _saveFilterState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(state.toJson());
      await prefs.setString(_timelineFilterKey, jsonString);
    } catch (e) {
      debugPrint('Failed to save timeline filter state: $e');
    }
  }

  void _debouncedSave() {
    _saveTimer?.cancel();
    _saveTimer = Timer(const Duration(milliseconds: 500), _saveFilterState);
  }

  void updateFilterType(TimelineFilterType filterType) {
    state = state.copyWith(filterType: filterType);
    _debouncedSave();
  }

  void updateSortBy(TimelineSortBy sortBy) {
    state = state.copyWith(sortBy: sortBy);
    _debouncedSave();
  }

  void updateSearchQuery(String query) {
    // Cancel previous timer
    _debounceTimer?.cancel();

    // For immediate clearing, don't debounce
    if (query.isEmpty) {
      state = state.copyWith(searchQuery: query);
      _debouncedSave();
      return;
    }

    // Debounce search input to prevent excessive filtering
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      state = state.copyWith(searchQuery: query);
      _debouncedSave();
    });
  }

  void updateCustomDateRange(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(
      customStartDate: startDate,
      customEndDate: endDate,
    );
    _debouncedSave();
  }

  void updateSelectedApp(int? appId) {
    state = state.copyWith(selectedAppId: appId);
    _debouncedSave();
  }

  void toggleShowAll() {
    state = state.copyWith(showAll: !state.showAll);
    _debouncedSave();
  }

  void clearFilters() {
    _debounceTimer?.cancel();
    _saveTimer?.cancel();
    state = const TimelineFilterState();
    _saveFilterState();
  }
}