import 'package:flutter/foundation.dart';
import '../../../data/models/app_model.dart';
import '../timeline/timeline_enums.dart';
import '../timeline/timeline_filter_state.dart';
import '../timeline/timeline_entries.dart';

/// Groups consecutive timeline entries with the same appId
List<TimelineEntry> groupTimelineEntries(
  List<TimelineModel> timelineData,
  Map<int, AppModel> appMap,
) {
  if (timelineData.isEmpty) return [];

  final List<TimelineEntry> groupedEntries = [];
  TimelineModel? currentGroupFirst;
  List<TimelineModel> currentGroup = [];

  for (int i = 0; i < timelineData.length; i++) {
    final session = timelineData[i];
    final app = appMap[session.appId];

    // If this is the first item or if it's the same app as the current group
    if (currentGroupFirst == null || currentGroupFirst.appId == session.appId) {
      currentGroupFirst ??= session;
      currentGroup.add(session);
      continue;
    }

    // We have a completed group, add it to the result
    if (currentGroup.length > 1) {
      // Group has multiple entries, create a GroupedTimelineEntry
      final groupApp = appMap[currentGroupFirst.appId] ??
          AppModel(id: currentGroupFirst.appId, name: 'Unknown Game');
      groupedEntries.add(GroupedTimelineEntry(
        sessions: List.unmodifiable(currentGroup),
        app: groupApp,
      ));
    } else {
      // Group has only one entry, create a SingleTimelineEntry
      groupedEntries.add(SingleTimelineEntry(currentGroupFirst));
    }

    // Start a new group
    currentGroupFirst = session;
    currentGroup = [session];
  }

  // Handle the last group
  if (currentGroup.isNotEmpty) {
    if (currentGroup.length > 1) {
      // Group has multiple entries, create a GroupedTimelineEntry
      final groupApp = appMap[currentGroupFirst!.appId] ??
          AppModel(id: currentGroupFirst!.appId, name: 'Unknown Game');
      groupedEntries.add(GroupedTimelineEntry(
        sessions: List.unmodifiable(currentGroup),
        app: groupApp,
      ));
    } else {
      // Group has only one entry, create a SingleTimelineEntry
      groupedEntries.add(SingleTimelineEntry(currentGroupFirst!));
    }
  }

  return groupedEntries;
}

/// Optimized filtering and sorting function with O(n) complexity
List<TimelineModel> filterAndSortTimelineOptimized(
  List<TimelineModel> timeline,
  Map<int, AppModel> appMap,
  TimelineFilterState filterState,
) {
  var filteredTimeline = <TimelineModel>[];

  // Pre-compute search query for efficiency
  final searchQuery = filterState.searchQuery.toLowerCase();
  final hasSearchQuery = searchQuery.isNotEmpty;

  // Pre-compute date ranges for time filtering
  final now = DateTime.now();
  DateTime? filterStartDate;
  DateTime? filterEndDate;

  (filterStartDate, filterEndDate) = switch (filterState.filterType) {
    TimelineFilterType.today => (
      DateTime(now.year, now.month, now.day),
      null
    ),
    TimelineFilterType.yesterday => (
      DateTime(now.year, now.month, now.day - 1),
      DateTime(now.year, now.month, now.day)
    ),
    TimelineFilterType.thisWeek => (
      DateTime(now.subtract(Duration(days: now.weekday - 1)).year,
             now.subtract(Duration(days: now.weekday - 1)).month,
             now.subtract(Duration(days: now.weekday - 1)).day),
      null
    ),
    TimelineFilterType.lastWeek => (
      DateTime(
        now.subtract(Duration(days: now.weekday)).subtract(const Duration(days: 6)).year,
        now.subtract(Duration(days: now.weekday)).subtract(const Duration(days: 6)).month,
        now.subtract(Duration(days: now.weekday)).subtract(const Duration(days: 6)).day
      ),
      DateTime(
        now.subtract(Duration(days: now.weekday)).year,
        now.subtract(Duration(days: now.weekday)).month,
        now.subtract(Duration(days: now.weekday)).day
      ).add(const Duration(days: 1))
    ),
    TimelineFilterType.thisMonth => (
      DateTime(now.year, now.month, 1),
      null
    ),
    TimelineFilterType.lastMonth => (
      DateTime(now.year, now.month - 1, 1),
      DateTime(now.year, now.month, 1)
    ),
    TimelineFilterType.custom => (
      filterState.customStartDate,
      filterState.customEndDate?.add(const Duration(days: 1))
    ),
    TimelineFilterType.all => (
      null,
      null
    ),
  };

  // Single pass filtering with O(n) complexity
  for (final session in timeline) {
    // Filter by app selection
    if (filterState.selectedAppId != null && session.appId != filterState.selectedAppId) {
      continue;
    }

    // Filter by search query
    if (hasSearchQuery) {
      final app = appMap[session.appId];
      final appName = (app?.name ?? 'Unknown Game').toLowerCase();
      if (!appName.contains(searchQuery)) {
        continue;
      }
    }

    // Filter by date range
    if (filterStartDate != null || filterEndDate != null) {
      if (session.date == null) continue;

      if (filterStartDate != null && session.date!.isBefore(filterStartDate)) {
        continue;
      }

      if (filterEndDate != null && session.date!.isAfter(filterEndDate)) {
        continue;
      }
    }

    filteredTimeline.add(session);
  }

  // Optimized sorting
  switch (filterState.sortBy) {
    case TimelineSortBy.dateNewest:
      filteredTimeline.sort((a, b) {
        if (a.date == null && b.date == null) return 0;
        if (a.date == null) return 1;
        if (b.date == null) return -1;
        return b.date!.compareTo(a.date!);
      });
      break;
    case TimelineSortBy.dateOldest:
      filteredTimeline.sort((a, b) {
        if (a.date == null && b.date == null) return 0;
        if (a.date == null) return 1;
        if (b.date == null) return -1;
        return a.date!.compareTo(b.date!);
      });
      break;
    case TimelineSortBy.durationLongest:
      filteredTimeline.sort((a, b) => (b.duration ?? 0).compareTo(a.duration ?? 0));
      break;
    case TimelineSortBy.durationShortest:
      filteredTimeline.sort((a, b) => (a.duration ?? 0).compareTo(b.duration ?? 0));
      break;
    case TimelineSortBy.appName:
      filteredTimeline.sort((a, b) {
        final appA = appMap[a.appId];
        final appB = appMap[b.appId];
        final nameA = appA?.name ?? 'Unknown Game';
        final nameB = appB?.name ?? 'Unknown Game';
        return nameA.compareTo(nameB);
      });
      break;
  }

  return filteredTimeline;
}

/// Get label for timeline filter type
String getTimelineFilterTypeLabel(TimelineFilterType type) {
  return switch (type) {
    TimelineFilterType.all => 'All Time',
    TimelineFilterType.today => 'Today',
    TimelineFilterType.yesterday => 'Yesterday',
    TimelineFilterType.thisWeek => 'This Week',
    TimelineFilterType.lastWeek => 'Last Week',
    TimelineFilterType.thisMonth => 'This Month',
    TimelineFilterType.lastMonth => 'Last Month',
    TimelineFilterType.custom => 'Custom Range',
  };
}

/// Get label for timeline sort by option
String getTimelineSortByLabel(TimelineSortBy sortBy) {
  return switch (sortBy) {
    TimelineSortBy.dateNewest => 'Newest First',
    TimelineSortBy.dateOldest => 'Oldest First',
    TimelineSortBy.durationLongest => 'Longest Sessions',
    TimelineSortBy.durationShortest => 'Shortest Sessions',
    TimelineSortBy.appName => 'Game Name',
  };
}