import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../app/design_system/components/inputs.dart';
import 'timeline_filter_notifier.dart';

class TimelineSearchField extends ConsumerStatefulWidget {
  @override
  TimelineSearchFieldState createState() => TimelineSearchFieldState();
}

class TimelineSearchFieldState extends ConsumerState<TimelineSearchField> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    final filterState = ref.read(timelineFilterProvider);
    _controller = TextEditingController(text: filterState.searchQuery);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Update the controller text when the filter state changes
    final filterState = ref.watch(timelineFilterProvider);
    if (_controller.text != filterState.searchQuery) {
      _controller.text = filterState.searchQuery;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ModernTextField(
      hintText: 'Search your gaming timeline...',
      controller: _controller,
      onChanged: (value) => ref.read(timelineFilterProvider.notifier).updateSearchQuery(value),
      prefixIcon: const Icon(Icons.search),
      variant: InputVariant.filled,
    );
  }
}