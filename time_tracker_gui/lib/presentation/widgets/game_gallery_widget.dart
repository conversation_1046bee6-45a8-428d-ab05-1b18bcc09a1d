import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../providers/poster_providers.dart';
import '../../data/models/poster_model.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/design_system/design_system.dart';
import 'game_poster_widget.dart';

class GameGalleryWidget extends ConsumerStatefulWidget {
  final bool showSearch;
  final int maxItems;
  final Function(Poster)? onGameTap;

  const GameGalleryWidget({
    super.key,
    this.showSearch = true,
    this.maxItems = 20,
    this.onGameTap,
  });

  @override
  ConsumerState<GameGalleryWidget> createState() => _GameGalleryWidgetState();
}

class _GameGalleryWidgetState extends ConsumerState<GameGalleryWidget> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // Watch search results if search is enabled, otherwise watch regular posters
    final postersAsyncValue = widget.showSearch 
        ? ref.watch(searchResultsProvider)
        : ref.watch(postersProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showSearch) ...[
          _buildSearchBar(theme, isDark),
          SizedBox(height: DesignTokens.spacingL),
        ],
        
        Expanded(
          child: postersAsyncValue.when(
            data: (posters) => _buildGameGrid(posters.take(widget.maxItems).toList()),
            loading: () => _buildLoadingGrid(),
            error: (error, stackTrace) => _buildErrorWidget(error.toString()),
          ),
        ),
      ],
    );
  }

  Widget _buildSearchBar(ThemeData theme, bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[800] : Colors.grey[100],
        borderRadius: BorderRadius.circular(DesignTokens.radiusL),
        border: Border.all(
          color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
        ),
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search games...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    ref.read(searchQueryProvider.notifier).state = '';
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: DesignTokens.spacingM,
            vertical: DesignTokens.spacingM,
          ),
        ),
        onChanged: (value) {
          ref.read(searchQueryProvider.notifier).state = value;
        },
      ),
    );
  }

  Widget _buildGameGrid(List<Poster> posters) {
    if (posters.isEmpty) {
      return _buildEmptyState();
    }

    return GridView.builder(
      padding: EdgeInsets.all(UIConstants.spacingM.w),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getCrossAxisCount(),
        childAspectRatio: 0.7, // Poster aspect ratio
        crossAxisSpacing: UIConstants.spacingM.w,
        mainAxisSpacing: UIConstants.spacingM.h,
      ),
      itemCount: posters.length,
      itemBuilder: (context, index) {
        final poster = posters[index];
        return GamePosterWidget(
          poster: poster,
          onTap: () => widget.onGameTap?.call(poster),
        );
      },
    );
  }

  Widget _buildLoadingGrid() {
    return GridView.builder(
      padding: EdgeInsets.all(UIConstants.spacingM.w),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getCrossAxisCount(),
        childAspectRatio: 0.7,
        crossAxisSpacing: UIConstants.spacingM.w,
        mainAxisSpacing: UIConstants.spacingM.h,
      ),
      itemCount: 12, // Show 12 loading placeholders
      itemBuilder: (context, index) => _buildLoadingCard(),
    );
  }

  Widget _buildLoadingCard() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[800] : Colors.grey[200],
        borderRadius: BorderRadius.circular(DesignTokens.radiusL),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.sp,
            color: Colors.red,
          ),
          SizedBox(height: UIConstants.spacingM.h),
          Text(
            'Failed to load games',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          SizedBox(height: UIConstants.spacingS.h),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: UIConstants.spacingL.h),
          ElevatedButton(
            onPressed: () {
              // Refresh the data
              ref.invalidate(postersProvider);
              ref.invalidate(searchResultsProvider);
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.videogame_asset_off,
            size: 64.sp,
            color: Colors.grey,
          ),
          SizedBox(height: UIConstants.spacingM.h),
          Text(
            'No games found',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          SizedBox(height: UIConstants.spacingS.h),
          Text(
            'Try adjusting your search terms',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  int _getCrossAxisCount() {
    final width = MediaQuery.of(context).size.width;
    if (width > UIConstants.tabletBreakpoint) {
      return 6; // Desktop: 6 columns
    } else if (width > UIConstants.mobileBreakpoint) {
      return 4; // Tablet: 4 columns
    } else {
      return 2; // Mobile: 2 columns
    }
  }
} 
