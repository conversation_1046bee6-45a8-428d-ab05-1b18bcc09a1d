import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';
import '../providers/state_providers.dart';
import '../providers/poster_providers.dart';
import '../providers/daily_session_provider.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../app/design_system/design_system.dart';
import '../../data/models/app_model.dart';
import '../../data/models/poster_model.dart';
import '../../util/time_util.dart';
import 'tracking_controls.dart';
import 'app_list_widget.dart';
import 'common_ui_components.dart';

class DashboardComponents {
  static Widget buildFrostedTrackingCard(AsyncValue trackingStatus, bool isDark, ThemeData theme, {bool isMobile = false}) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(DesignTokens.radiusXL),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          padding: EdgeInsets.all(DesignTokens.spacingM),
          decoration: BoxDecoration(
            color: isDark
              ? Colors.black.withOpacity(0.3)
              : theme.colorScheme.surface.withOpacity(0.8),
            borderRadius: BorderRadius.circular(DesignTokens.radiusXL),
            border: Border.all(
              color: isDark
                ? Colors.white.withOpacity(0.2)
                : AppTheme.neutralGray300.withOpacity(0.5),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(DesignTokens.spacingM),
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(DesignTokens.radiusM),
                      border: Border.all(
                        color: AppTheme.successColor.withOpacity(0.5),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      Icons.track_changes,
                      color: AppTheme.successColor,
                      size: DesignTokens.iconM,
                    ),
                  ),
                  SizedBox(width: DesignTokens.spacingM),
                  Text(
                    'Session Tracking',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: isDark ? Colors.white : AppTheme.neutralGray900,
                    ),
                  ),
                ],
              ),
              SizedBox(height: DesignTokens.spacingM),
              trackingStatus.when(
                data: (status) => TrackingControls(status: status),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, _) => CommonUIComponents.buildErrorState('Failed to load tracking status'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Widget buildEnhancedStatsGrid(AsyncValue trackingStatus, AsyncValue apps, bool isDark, ThemeData theme) {
    return ResponsiveLayout(
      mobile: ModernColumn(
        spacing: DesignTokens.spacingM,
        children: [
          _buildEnhancedTotalGamesCard(apps, isDark, theme),
          _buildEnhancedTodayTimeCard(isDark, theme),
        ],
      ),
      tablet: ModernRow(
        spacing: DesignTokens.spacingL,
        children: [
          Expanded(child: _buildEnhancedTotalGamesCard(apps, isDark, theme)),
          Expanded(child: _buildEnhancedTodayTimeCard(isDark, theme)),
        ],
      ),
      desktop: ModernRow(
        spacing: DesignTokens.spacingL,
        children: [
          Expanded(child: _buildEnhancedTotalGamesCard(apps, isDark, theme)),
          Expanded(child: _buildEnhancedTodayTimeCard(isDark, theme)),
        ],
      ),
    );
  }

  static Widget _buildEnhancedTotalGamesCard(AsyncValue apps, bool isDark, ThemeData theme) {
    return apps.when(
      data: (appList) => ModernStatsCard(
        title: 'Games Library',
        value: '${appList.length}',
        subtitle: 'games tracked',
        icon: Icons.library_books,
        accentColor: isDark ? AppColors.primary500 : theme.colorScheme.primary,
      ),
      loading: () => ModernStatsCard(
        title: 'Games Library',
        value: '...',
        subtitle: 'loading',
        icon: Icons.library_books,
        accentColor: AppColors.neutral500,
      ),
      error: (_, __) => ModernStatsCard(
        title: 'Games Library',
        value: 'Error',
        subtitle: 'failed to load',
        icon: Icons.error_outline,
        accentColor: AppColors.error,
      ),
    );
  }

  static Widget _buildEnhancedTodayTimeCard(bool isDark, ThemeData theme) {
    return Consumer(
      builder: (context, ref, child) {
        final todaySession = ref.watch(todaySessionProvider);

        return todaySession.when(
          data: (data) => ModernStatsCard(
            title: 'Today\'s Playtime',
            value: TimeUtil.formatDurationFromMinutes(data.totalMinutes),
            subtitle: 'gaming session',
            icon: Icons.schedule,
            accentColor: AppColors.success,
          ),
          loading: () => ModernStatsCard(
            title: 'Today\'s Playtime',
            value: '...',
            subtitle: 'loading',
            icon: Icons.schedule,
            accentColor: AppColors.neutral500,
          ),
          error: (_, __) => ModernStatsCard(
            title: 'Today\'s Playtime',
            value: '0m',
            subtitle: 'no data',
            icon: Icons.schedule,
            accentColor: AppColors.success,
          ),
        );
      },
    );
  }

  static Widget _buildEnhancedStatDisplay(String value, String subtitle, IconData icon, Color color, bool isDark, ThemeData theme, {bool isMobile = false}) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(DesignTokens.spacingM),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(DesignTokens.radiusL),
            border: Border.all(
              color: color.withOpacity(0.5),
              width: 2,
            ),
          ),
          child: Icon(
            icon,
            color: color,
            size: DesignTokens.iconL,
          ),
        ),
        SizedBox(width: DesignTokens.spacingM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: color,
                ),
              ),
              Text(
                subtitle,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isDark ? Colors.white.withOpacity(0.6) : AppTheme.neutralGray600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  static Widget buildGamingAppsSection(AsyncValue<List<AppModel>> apps, bool isDark, ThemeData theme, VoidCallback onViewAll) {
    return ModernInfoCard(
      title: 'Recent Games',
      icon: Icons.games,
      actions: [
        PrimaryButton(
          onPressed: onViewAll,
          size: ButtonSize.small,
          child: const Text('View All'),
        ),
      ],
      child: apps.when(
        data: (appList) => appList.isEmpty
            ? _buildEmptyGamesState()
            : AppListWidget(
                apps: appList.take(5).toList(),
                isCompact: true,
              ),
        loading: () => const ModernContainer(
          padding: EdgeInsets.all(DesignTokens.spacingXL),
          child: Center(child: LoadingSpinner()),
        ),
        error: (error, _) => _buildErrorGamesState(),
      ),
    );
  }

  static Widget _buildEmptyGamesState() {
    return ModernContainer(
      padding: const EdgeInsets.all(DesignTokens.spacingXL),
      child: ModernColumn(
        spacing: DesignTokens.spacingM,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.games_outlined,
            size: 48,
            color: AppColors.neutral500,
          ),
          Text(
            'No games tracked yet',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.neutral700,
            ),
          ),
          Text(
            'Start playing to see your library here',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.neutral500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  static Widget _buildErrorGamesState() {
    return ModernContainer(
      padding: const EdgeInsets.all(DesignTokens.spacingXL),
      child: ModernColumn(
        spacing: DesignTokens.spacingM,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: AppColors.error,
          ),
          Text(
            'Failed to load games',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.error,
            ),
          ),
        ],
      ),
    );
  }

}
