import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/models/app_model.dart';
import '../../providers/state_providers.dart';
import '../../../util/time_util.dart';
import '../../../app/design_system/design_system.dart';

class LongestSessionCard extends ConsumerWidget {
  final int appId;

  const LongestSessionCard({
    super.key,
    required this.appId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appsAsync = ref.watch(appsProvider);

    return appsAsync.when(
      data: (apps) {
        final app = apps.firstWhere((a) => a.id == appId, orElse: () => const AppModel(id: -1));
        if (app.id == -1) {
          return const SizedBox();
        }

        final longestSession = app.longestSession ?? 0;
        final formattedDuration = TimeUtil.formatDurationFromMinutesAsHours(longestSession);
        final screenWidth = MediaQuery.of(context).size.width;
        final isMobile = screenWidth < 600; // Mobile breakpoint

        return ModernInfoCard(
          title: 'Longest Session',
          icon: Icons.timer,
          child: ModernColumn(
            spacing: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingS,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                formattedDuration,
                style: TextStyle(
                  fontSize: isMobile ? 24 : 32, // Smaller font on mobile
                  fontWeight: FontWeight.bold,
                  color: AppColors.steamBlue,
                ),
              ),
              Text(
                'Your longest gaming session for this app',
                style: TextStyle(
                  fontSize: isMobile ? 12 : 14, // Smaller font on mobile
                  color: AppColors.neutral600,
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const SizedBox(),
      error: (_, __) => const SizedBox(),
    );
  }
}