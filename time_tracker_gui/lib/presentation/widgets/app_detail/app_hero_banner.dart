import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';

import '../../../data/models/app_model.dart';
import '../../../data/models/poster_model.dart';
import '../../providers/local_game_poster_providers.dart';
import '../../providers/state_providers.dart';
import '../../../core/constants/ui_constants.dart';
import '../../../app/theme/app_theme.dart';
import '../../../util/time_util.dart';
import '../../../app/design_system/design_system.dart';

class AppHeroBanner extends ConsumerWidget {
  final AppModel app;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const AppHeroBanner({
    super.key,
    required this.app,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final gameName = _getGameName(app);
    final poster = ref.watch(cachedPosterProvider(gameName));
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return SliverAppBar(
      expandedHeight: 300.h,
      pinned: true,
      stretch: true,
      backgroundColor: isDark ? theme.darkBg : theme.colorScheme.surface,
      title: Text(
        app.name ?? 'Unknown App',
        style: theme.textTheme.titleLarge?.copyWith(
          color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
          fontWeight: FontWeight.w600,
          shadows: [
            Shadow(
              color: isDark
                ? Colors.black.withOpacity(0.5)
                : Colors.white.withOpacity(0.5),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
      ),
      flexibleSpace: FlexibleSpaceBar(
        background: _buildBackground(poster, isDark, theme, ref),
        stretchModes: const [
          StretchMode.zoomBackground,
          StretchMode.blurBackground,
        ],
      ),
      actions: [
        _buildActionButton(
          icon: Icons.edit,
          color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
          onPressed: onEdit,
          isDark: isDark,
        ),
        _buildActionButton(
          icon: Icons.delete,
          color: isDark ? AppTheme.accentColor : theme.colorScheme.error,
          onPressed: onDelete,
          isDark: isDark,
          isLast: true,
        ),
      ],
    );
  }

  String _getGameName(AppModel app) {
    String gameName = '';

    if (app.productName?.isNotEmpty == true) {
      gameName = app.productName!;
    } else if (app.name?.isNotEmpty == true) {
      gameName = app.name!;
    }

    // Clean up the game name for better search results
    gameName = gameName
        .replaceAll(RegExp(r'\.exe$', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();

    return gameName;
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
    required bool isDark,
    bool isLast = false,
  }) {
    return Container(
      margin: EdgeInsets.only(
        right: isLast ? DesignTokens.spacingM : DesignTokens.spacingS,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: isDark
                ? Colors.white.withOpacity(0.1)
                : Colors.black.withOpacity(0.1),
              borderRadius: BorderRadius.circular(DesignTokens.radiusM),
              border: Border.all(
                color: isDark
                  ? Colors.white.withOpacity(0.2)
                  : Colors.black.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: IconButton(
              icon: Icon(icon, color: color),
              onPressed: onPressed,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBackground(AsyncValue<Poster?> poster, bool isDark, ThemeData theme, WidgetRef ref) {
    return Stack(
      fit: StackFit.expand,
      children: [
        _buildBackgroundImage(poster, isDark),
        _buildGradientOverlay(isDark, theme),
        _buildContentOverlay(isDark, theme, ref),
      ],
    );
  }

  Widget _buildBackgroundImage(AsyncValue<Poster?> poster, bool isDark) {
    return poster.when(
      data: (posterData) => posterData != null && posterData.hasImage
        ? Image.network(
            posterData.backgroundImage,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => _buildDefaultBackground(isDark),
          )
        : _buildDefaultBackground(isDark),
      loading: () => _buildDefaultBackground(isDark),
      error: (error, stackTrace) => _buildDefaultBackground(isDark),
    );
  }

  Widget _buildDefaultBackground(bool isDark) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDark ? [
            AppTheme.primaryColor.withOpacity(0.3),
            AppTheme.accentColor.withOpacity(0.2),
            Colors.black,
          ] : [
            AppTheme.primaryColor.withOpacity(0.2),
            AppTheme.secondaryColor.withOpacity(0.1),
            Colors.white,
          ],
        ),
      ),
      child: Center(
        child: Icon(
          Icons.games,
          size: 120.sp,
          color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray400,
        ),
      ),
    );
  }

  Widget _buildGradientOverlay(bool isDark, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: isDark ? [
            Colors.transparent,
            theme.darkBg.withOpacity(0.3),
            theme.darkBg.withOpacity(0.8),
            theme.darkBg,
          ] : [
            Colors.transparent,
            Colors.white.withOpacity(0.3),
            Colors.white.withOpacity(0.8),
            Colors.white,
          ],
          stops: const [0.0, 0.4, 0.8, 1.0],
        ),
      ),
    );
  }

  Widget _buildContentOverlay(bool isDark, ThemeData theme, WidgetRef ref) {
    return Positioned(
      bottom: DesignTokens.spacingXL,
      left: 0,
      right: 0,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: DesignTokens.spacingL),
        child: Center(
          child: ConstrainedBox(
            constraints: BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(DesignTokens.radiusL),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                child: Container(
                  padding: EdgeInsets.all(DesignTokens.spacingL),
                  decoration: BoxDecoration(
                    color: isDark
                      ? Colors.white.withOpacity(0.1)
                      : Colors.black.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(DesignTokens.radiusL),
                    border: Border.all(
                      color: isDark
                        ? Colors.white.withOpacity(0.2)
                        : Colors.black.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: _buildAppInfo(isDark, theme, ref),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppInfo(bool isDark, ThemeData theme, WidgetRef ref) {
    final appsAsync = ref.watch(appsProvider);

    return appsAsync.when(
      data: (apps) {
        // Find the current app with live data from the provider
        final liveApp = apps.firstWhere(
          (a) => a.id == app.id,
          orElse: () => app, // Fallback to original app if not found
        );

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              liveApp.name ?? 'Unknown App',
              style: theme.textTheme.headlineLarge?.copyWith(
                color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                fontWeight: FontWeight.w700,
                fontSize: 36.sp,
                shadows: [
                  Shadow(
                    color: isDark ? Colors.black : Colors.white,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
            ),
            SizedBox(height: DesignTokens.spacingM),
            Row(
              children: [
                Text(
                  'Total Playtime: ',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  TimeUtil.formatMinutes(liveApp.duration ?? 0), // Use live app data!
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: isDark ? AppTheme.primaryColor : AppTheme.primaryDark,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        );
      },
      loading: () => _buildAppInfoFallback(isDark, theme),
      error: (_, __) => _buildAppInfoFallback(isDark, theme),
    );
  }

  Widget _buildAppInfoFallback(bool isDark, ThemeData theme) {
    // Fallback to the original app data if provider fails
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          app.name ?? 'Unknown App',
          style: theme.textTheme.headlineLarge?.copyWith(
            color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
            fontWeight: FontWeight.w700,
            fontSize: 36.sp,
            shadows: [
              Shadow(
                color: isDark ? Colors.black : Colors.white,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
        ),
        SizedBox(height: DesignTokens.spacingM),
        Row(
          children: [
            Text(
              'Total Playtime: ',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              TimeUtil.formatMinutes(app.duration ?? 0),
              style: theme.textTheme.bodyLarge?.copyWith(
                color: isDark ? AppTheme.primaryColor : AppTheme.primaryDark,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
