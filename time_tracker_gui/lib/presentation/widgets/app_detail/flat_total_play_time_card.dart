import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/models/app_model.dart';
import '../../providers/state_providers.dart';
import '../../../util/time_util.dart';
import '../../../app/design_system/design_system.dart';
import '../../../app/theme/app_theme.dart';

class FlatTotalPlayTimeCard extends ConsumerWidget {
  final int appId;

  const FlatTotalPlayTimeCard({
    super.key,
    required this.appId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appsAsync = ref.watch(appsProvider);

    return appsAsync.when(
      data: (apps) {
        final app = apps.firstWhere((a) => a.id == appId, orElse: () => const AppModel(id: -1));
        if (app.id == -1) {
          return const SizedBox();
        }

        final totalPlayTime = app.duration ?? 0;
        final formattedDuration = TimeUtil.formatDurationFromMinutesAsHours(totalPlayTime);
        final screenWidth = MediaQuery.of(context).size.width;
        final isMobile = screenWidth < 600; // Mobile breakpoint
        final theme = Theme.of(context);
        final isDark = theme.brightness == Brightness.dark;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  color: theme.colorScheme.primary,
                  size: isMobile ? DesignTokens.iconS : DesignTokens.iconM,
                ),
                SizedBox(width: isMobile ? DesignTokens.spacingS : DesignTokens.spacingM),
                Text(
                  'Total Play Time',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: isMobile ? 14 : null,
                  ),
                ),
              ],
            ),
            SizedBox(height: isMobile ? DesignTokens.spacingS : DesignTokens.spacingM),
            Text(
              formattedDuration,
              style: TextStyle(
                fontSize: isMobile ? 24 : 32,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            Text(
              'Total time spent on this app',
              style: TextStyle(
                fontSize: isMobile ? 12 : 14,
                color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
              ),
            ),
          ],
        );
      },
      loading: () => const SizedBox(),
      error: (_, __) => const SizedBox(),
    );
  }
}