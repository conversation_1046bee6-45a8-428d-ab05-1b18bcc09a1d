import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/models/app_model.dart';
import '../../providers/state_providers.dart';
import '../../providers/search_filter_providers.dart';
import '../../../app/theme/app_theme.dart';
import '../../../app/design_system/design_system.dart';
import '../app_detail/app_detail_enums.dart';

class FlatAppStatsRow extends ConsumerWidget {
  final int appId;

  const FlatAppStatsRow({
    super.key,
    required this.appId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appsAsync = ref.watch(appsProvider);
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return appsAsync.when(
      data: (apps) {
        final app = apps.firstWhere((a) => a.id == appId, orElse: () => const AppModel(id: -1));
        if (app.id == -1) {
          return const SizedBox();
        }

        // Use Column layout on very small screens to prevent overflow
        if (isMobile && screenWidth < 500) {
          return ModernColumn(
            spacing: DesignTokens.spacingS,
            children: [
              _buildStatItem(
                context,
                theme,
                isDark,
                Icons.launch,
                'Launches',
                '${app.launches ?? 0}',
                AppColors.steamBlue,
              ),
              _buildStatItem(
                context,
                theme,
                isDark,
                Icons.timer,
                'Avg Session',
                _formatDuration(_calculateAverageSession(app)),
                AppColors.success,
              ),
              _buildStatItem(
                context,
                theme,
                isDark,
                Icons.schedule,
                'Last Played',
                _formatLastPlayed(ref, app),
                AppColors.warning,
              ),
            ],
          );
        }

        // Use intrinsic width for mobile to prevent overflow
        if (isMobile) {
          return IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    theme,
                    isDark,
                    Icons.launch,
                    'Launches',
                    '${app.launches ?? 0}',
                    AppColors.steamBlue,
                  ),
                ),
                SizedBox(width: DesignTokens.spacingS),
                Expanded(
                  child: _buildStatItem(
                    context,
                    theme,
                    isDark,
                    Icons.timer,
                    'Avg Session',
                    _formatDuration(_calculateAverageSession(app)),
                    AppColors.success,
                  ),
                ),
                SizedBox(width: DesignTokens.spacingS),
                Expanded(
                  child: _buildStatItem(
                    context,
                    theme,
                    isDark,
                    Icons.schedule,
                    'Last Played',
                    _formatLastPlayed(ref, app),
                    AppColors.warning,
                  ),
                ),
              ],
            ),
          );
        }

        // Create 3 columns with dividers, each column centered
        return Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // First column - Launches
              Expanded(
                child: _buildStatItem(
                  context,
                  theme,
                  isDark,
                  Icons.launch,
                  'Launches',
                  '${app.launches ?? 0}',
                  AppColors.steamBlue,
                ),
              ),
              // Divider
              Container(
                width: 1,
                height: 50,
                decoration: BoxDecoration(
                  color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
                ),
              ),
              // Second column - Avg Session
              Expanded(
                child: _buildStatItem(
                  context,
                  theme,
                  isDark,
                  Icons.timer,
                  'Avg Session',
                  _formatDuration(_calculateAverageSession(app)),
                  AppColors.success,
                ),
              ),
              // Divider
              Container(
                width: 1,
                height: 50,
                decoration: BoxDecoration(
                  color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
                ),
              ),
              // Third column - Last Played
              Expanded(
                child: _buildStatItem(
                  context,
                  theme,
                  isDark,
                  Icons.schedule,
                  'Last Played',
                  _formatLastPlayed(ref, app),
                  AppColors.warning,
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const SizedBox(),
      error: (_, __) => const SizedBox(),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    ThemeData theme,
    bool isDark,
    IconData icon,
    String title,
    String value,
    Color accentColor,
  ) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(isMobile ? DesignTokens.spacingXS : DesignTokens.spacingS),
              decoration: BoxDecoration(
                color: accentColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(DesignTokens.radiusS),
              ),
              child: Icon(
                icon,
                color: accentColor,
                size: isMobile ? DesignTokens.iconXS : DesignTokens.iconS,
              ),
            ),
            SizedBox(width: isMobile ? DesignTokens.spacingS : DesignTokens.spacingM),
            Text(
              title,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                fontSize: isMobile ? 12 : null,
              ),
            ),
          ],
        ),
        SizedBox(height: isMobile ? DesignTokens.spacingS : DesignTokens.spacingM),
        Text(
          value,
          style: theme.textTheme.headlineMedium?.copyWith(
            color: accentColor,
            fontWeight: FontWeight.w700,
            fontSize: isMobile ? 20 : null,
          ),
        ),
      ],
    );
  }

  int _calculateAverageSession(AppModel app) {
    final totalDuration = app.duration ?? 0;
    final launches = app.launches ?? 1;
    return launches > 0 ? totalDuration ~/ launches : 0;
  }

  String _formatLastPlayed(WidgetRef ref, AppModel app) {
    // Use the cached apps with last used provider for better performance
    final appsWithLastUsed = ref.watch(appsWithLastUsedCacheProvider);

    return appsWithLastUsed.when(
      data: (appWithLastUsedList) {
        // Find this app in the list
        final appWithLastUsed = appWithLastUsedList
            .where((item) => item.app.id == app.id)
            .firstOrNull;

        if (appWithLastUsed?.lastUsedDate == null) return 'Never';

        final lastDate = appWithLastUsed!.lastUsedDate!;
        final now = DateTime.now();
        final difference = now.difference(lastDate).inDays;

        return switch (difference) {
          0 => 'Today',
          1 => 'Yesterday',
          < 7 => '${difference}d ago',
          < 30 => '${difference ~/ 7}w ago',
          < 365 => '${difference ~/ 30}mo ago',
          _ => _formatActualDate(lastDate),
        };
      },
      loading: () => '...',
      error: (_, __) => 'Never',
    );
  }

  String _formatActualDate(DateTime date) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];

    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  String _formatDuration(int minutes) {
    return minutes < 60
        ? '${minutes}m'
        : '${minutes ~/ 60}h ${minutes % 60}m';
  }
}