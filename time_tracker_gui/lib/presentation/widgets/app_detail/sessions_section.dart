import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../data/models/app_model.dart';
import '../../providers/app_timeline_providers.dart';
import '../../../core/constants/ui_constants.dart';
import '../../../app/design_system/design_system.dart';
import 'app_detail_enums.dart';
import '../../../app/theme/app_theme.dart';
import '../../../util/time_util.dart';
import 'app_detail_enums.dart';
import 'session_service.dart';

class SessionsSection extends ConsumerWidget {
  final String appId;
  final TimeRange selectedTimeRange;
  final CheckpointModel? selectedCheckpoint;
  final SessionSortOption selectedSortOption;
  final bool showAllSessions;
  final ValueChanged<SessionSortOption> onSortChanged;
  final VoidCallback onToggleShowAll;

  static const int defaultSessionLimit = 10;

  const SessionsSection({
    super.key,
    required this.appId,
    required this.selectedTimeRange,
    this.selectedCheckpoint,
    required this.selectedSortOption,
    required this.showAllSessions,
    required this.onSortChanged,
    required this.onToggleShowAll,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dateRange = selectedTimeRange.getDateRange();
    final timeline = ref.watch(appTimelineProvider(AppTimelineParams(
      appId: int.parse(appId),
      startDate: dateRange?.start,
      endDate: dateRange?.end,
    )));
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(theme, isDark),
        SizedBox(height: DesignTokens.spacingL),
        _buildSessionsList(timeline, theme, isDark),
      ],
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDark) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Gaming Sessions',
                style: theme.textTheme.titleLarge?.copyWith(
                  color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                  fontWeight: FontWeight.w700,
                ),
              ),
              if (selectedCheckpoint != null) ...[
                SizedBox(height: DesignTokens.spacingS),
                Row(
                  children: [
                    Container(
                      width: 8.w,
                      height: 8.h,
                      decoration: BoxDecoration(
                        color: Color(int.parse(selectedCheckpoint!.color.replaceFirst('#', '0xFF'))),
                        shape: BoxShape.circle,
                      ),
                    ),
                    SizedBox(width: DesignTokens.spacingS),
                    Text(
                      'Filtered by ${selectedCheckpoint!.name}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
        _buildSortDropdown(theme, isDark),
      ],
    );
  }

  Widget _buildSortDropdown(ThemeData theme, bool isDark) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: DesignTokens.spacingM),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        border: Border.all(
          color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray400,
          width: 1,
        ),
      ),
      child: DropdownButton<SessionSortOption>(
        value: selectedSortOption,
        underline: const SizedBox(),
        dropdownColor: isDark ? theme.cardBg : theme.colorScheme.surface,
        style: theme.textTheme.bodySmall?.copyWith(
          color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
        ),
        items: SessionSortOption.values.map((option) => DropdownMenuItem<SessionSortOption>(
          value: option,
          child: Text(option.displayName),
        )).toList(),
        onChanged: (SessionSortOption? value) => value != null ? onSortChanged(value) : null,
      ),
    );
  }

  Widget _buildSessionsList(AsyncValue<List<TimelineModel>> timeline, ThemeData theme, bool isDark) {
    return timeline.when(
      data: (timelineData) => _buildSessionsData(timelineData, theme, isDark),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) => _buildErrorState('Failed to load sessions', isDark, theme),
    );
  }

  Widget _buildSessionsData(List<TimelineModel> timelineData, ThemeData theme, bool isDark) {
    final filteredSessions = SessionService.filterSessions(timelineData, selectedTimeRange, selectedCheckpoint);
    final sortedSessions = SessionService.sortSessions(filteredSessions, selectedSortOption);

    if (sortedSessions.isEmpty) {
      return _buildEmptySessionsState(isDark, theme);
    }

    final sessionsToShow = showAllSessions
      ? sortedSessions
      : sortedSessions.take(defaultSessionLimit).toList();

    return Column(
      children: [
        ...sessionsToShow.map((session) => _buildSessionItem(session, isDark, theme)),
        if (sortedSessions.length > defaultSessionLimit)
          _buildShowMoreButton(sortedSessions.length, isDark, theme),
      ],
    );
  }

  Widget _buildSessionItem(TimelineModel session, bool isDark, ThemeData theme) {
    final primaryColor = isDark ? AppTheme.primaryColor : theme.colorScheme.primary;
    final hasCheckpoint = session.checkpointId != null || session.checkpointAssociations.isNotEmpty;

    return Container(
      margin: EdgeInsets.only(bottom: DesignTokens.spacingM),
      padding: EdgeInsets.all(DesignTokens.spacingL),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        border: Border.all(
          color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 6.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: primaryColor,
              borderRadius: BorderRadius.circular(3),
            ),
          ),
          SizedBox(width: DesignTokens.spacingL),
          Icon(
            Icons.timer,
            color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
            size: UIConstants.iconM,
          ),
          SizedBox(width: DesignTokens.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  TimeUtil.formatMinutes(session.duration ?? 0),
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (hasCheckpoint && selectedCheckpoint == null) ...[
                  SizedBox(height: DesignTokens.spacingS),
                  Row(
                    children: [
                      Icon(
                        Icons.bookmark,
                        size: 12.w,
                        color: isDark ? AppTheme.neutralGray500 : AppTheme.neutralGray600,
                      ),
                      SizedBox(width: DesignTokens.spacingS),
                      Text(
                        session.checkpointAssociations.isNotEmpty
                          ? '${session.checkpointAssociations.length} checkpoint${session.checkpointAssociations.length > 1 ? 's' : ''}'
                          : 'Checkpoint session',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: isDark ? AppTheme.neutralGray500 : AppTheme.neutralGray600,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
          Text(
            session.date?.toString().split(' ')[0] ?? 'Unknown date',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShowMoreButton(int totalSessions, bool isDark, ThemeData theme) {
    final primaryColor = isDark ? AppTheme.primaryColor : theme.colorScheme.primary;
    final sessionsShown = showAllSessions ? totalSessions : defaultSessionLimit;

    return Container(
      margin: EdgeInsets.only(top: DesignTokens.spacingM),
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onToggleShowAll,
        style: OutlinedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: DesignTokens.spacingM),
          side: BorderSide(
            color: primaryColor.withOpacity(0.5),
            width: 1,
          ),
          backgroundColor: primaryColor.withOpacity(0.05),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignTokens.radiusM),
          ),
        ),
        icon: Icon(
          showAllSessions ? Icons.expand_less : Icons.expand_more,
          color: primaryColor,
        ),
        label: Text(
          showAllSessions
            ? 'Show Less'
            : 'Showing $sessionsShown of $totalSessions sessions',
          style: TextStyle(color: primaryColor),
        ),
      ),
    );
  }

  Widget _buildEmptySessionsState(bool isDark, ThemeData theme) {
    final filterDesc = SessionService.getFilterDescription(selectedTimeRange, selectedCheckpoint);

    return Container(
      padding: EdgeInsets.all(DesignTokens.spacingXL),
      decoration: BoxDecoration(
        color: isDark
          ? theme.darkBg.withOpacity(0.5)
          : theme.colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        border: Border.all(
          color: isDark
            ? AppTheme.neutralGray700.withOpacity(0.5)
            : AppTheme.neutralGray400.withOpacity(0.3),
        ),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.history_outlined,
              size: UIConstants.iconXL,
              color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray500,
            ),
            SizedBox(height: DesignTokens.spacingM),
            Text(
              'No sessions found',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
              ),
            ),
            SizedBox(height: DesignTokens.spacingM),
            Text(
              'No gaming sessions for $filterDesc',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark ? AppTheme.neutralGray500 : AppTheme.neutralGray600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String message, bool isDark, ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(DesignTokens.spacingXL),
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        border: Border.all(
          color: theme.colorScheme.error.withOpacity(0.3),
        ),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              size: UIConstants.iconXL,
              color: theme.colorScheme.error,
            ),
            SizedBox(height: DesignTokens.spacingM),
            Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
