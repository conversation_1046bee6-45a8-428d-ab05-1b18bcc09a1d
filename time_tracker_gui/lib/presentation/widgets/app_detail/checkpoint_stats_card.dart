import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/models/app_model.dart';
import '../../providers/checkpoint_providers.dart';
import '../../../util/time_util.dart';
import '../../../app/design_system/design_system.dart';

class CheckpointStatsCard extends ConsumerWidget {
  final CheckpointModel checkpoint;
  final String appId;

  const CheckpointStatsCard({
    super.key,
    required this.checkpoint,
    required this.appId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final checkpointStatsAsync = ref.watch(checkpointStatsProvider(checkpoint.id));
    final checkpointColor = Color(int.parse(checkpoint.color.replaceFirst('#', '0xFF')));
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    return ModernInfoCard(
      title: checkpoint.name,
      icon: Icons.flag,
      child: ModernColumn(
        spacing: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingS,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ModernRow(
            spacing: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingS,
            children: [
              Container(
                width: isMobile ? 12 : 16,
                height: isMobile ? 12 : 16,
                decoration: BoxDecoration(
                  color: checkpointColor,
                  shape: BoxShape.circle,
                ),
              ),
              if (checkpoint.description != null)
                Expanded(
                  child: Text(
                    checkpoint.description!,
                    style: TextStyle(
                      fontSize: isMobile ? 12 : 14,
                      color: AppColors.neutral600,
                    ),
                  ),
                ),
            ],
          ),
          checkpointStatsAsync.when(
            data: (stats) {
              if (stats.isEmpty) {
                return const Text(
                  'No data available for this checkpoint',
                  style: TextStyle(color: AppColors.neutral600),
                );
              }
              final totalDuration = stats.fold<int>(0, (sum, stat) => sum + (stat.duration ?? 0));
              final totalSessions = stats.fold<int>(0, (sum, stat) => sum + (stat.sessionsCount ?? 0));

              return ModernRow(
                spacing: DesignTokens.spacingM,
                children: [
                  Expanded(
                    child: ModernStatsCard(
                      title: 'Total Time',
                      value: TimeUtil.formatDuration(totalDuration),
                      icon: Icons.timer,
                      accentColor: checkpointColor,
                    ),
                  ),
                  Expanded(
                    child: ModernStatsCard(
                      title: 'Sessions',
                      value: '$totalSessions',
                      icon: Icons.play_arrow,
                      accentColor: AppColors.success,
                    ),
                  ),
                ],
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, _) => const Text(
              'Error loading checkpoint stats',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }


}