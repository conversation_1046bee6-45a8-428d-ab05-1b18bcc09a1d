import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:ui';

import '../../app/design_system/design_system.dart';
import '../../data/models/app_model.dart';
import '../providers/app_providers.dart';
import '../widgets/shared_hero_components.dart';
import '../widgets/analytics/stats_cards.dart';
import '../widgets/analytics/playtime_chart.dart';
import '../widgets/analytics/playtime_per_weekday.dart';
import '../widgets/analytics/top_apps_by_playtime.dart';

class AnalyticsPage extends ConsumerStatefulWidget {
  const AnalyticsPage({super.key});

  @override
  ConsumerState<AnalyticsPage> createState() => _AnalyticsPageState();
}

class _AnalyticsPageState extends ConsumerState<AnalyticsPage> {
  int _selectedTab = 0; // 0: Overview, 1: Games

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    return Container(
      color: isDark ? AppColors.darkSurface1 : AppColors.neutral0,
      child:
        SingleChildScrollView(
          padding: EdgeInsets.all(DesignTokens.spacingM),
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
              child: Column(
                children: [
              // Stats Cards
              StatsCards(isMobile: isMobile, isDark: isDark),

              SizedBox(height: isMobile ? DesignTokens.spacingM : DesignTokens.spacingL),

              // Playtime Chart
              PlaytimeChart(isMobile: isMobile, isDark: isDark),

              SizedBox(height: isMobile ? DesignTokens.spacingM : DesignTokens.spacingL),

              // Genres Played
              PlaytimePerWeekday(isMobile: isMobile, isDark: isDark),

              SizedBox(height: isMobile ? DesignTokens.spacingM : DesignTokens.spacingL),

              // Achievements
              PlayTimePerWeekdaySection(isMobile: isMobile, isDark: isDark),
                ],
              ),
            ),
          ),
        ),
    );
  }
}
