import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:async';
import '../../data/models/app_model.dart';
import '../providers/checkpoint_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../app/design_system/design_system.dart';
import '../../util/time_util.dart';

class CheckpointDialog extends ConsumerStatefulWidget {
  final AppModel app;

  const CheckpointDialog({
    super.key,
    required this.app,
  });

  @override
  ConsumerState<CheckpointDialog> createState() => _CheckpointDialogState();
}

class _CheckpointDialogState extends ConsumerState<CheckpointDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  DateTime _validFrom = DateTime.now();
  String _selectedColor = '#2196F3';

  // State for selected checkpoint
  CheckpointWithStatus? _selectedCheckpoint;

  // State for the two-panel layout
  bool _showCreatePanel = false;

  final List<String> _colorOptions = [
    '#2196F3', // Blue
    '#4CAF50', // Green
    '#FF9800', // Orange
    '#F44336', // Red
    '#9C27B0', // Purple
    '#607D8B', // Blue Grey
    '#795548', // Brown
    '#FF5722', // Deep Orange
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  void _refreshCheckpointData() {
    invalidateCheckpointCache(ref, widget.app.id);

    ref.read(checkpointsForAppProvider(widget.app.id).notifier).refresh();
    ref.read(activeCheckpointForAppProvider(widget.app.id).notifier).refresh();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _toggleCreatePanel() {
    setState(() {
      _showCreatePanel = !_showCreatePanel;
      if (_showCreatePanel) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  void _selectCheckpoint(CheckpointWithStatus checkpoint) {
    setState(() {
      _selectedCheckpoint = checkpoint;
      _showCreatePanel = false;
      _animationController.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusXL),
      ),
      child: Container(
        width: 900.w,
        height: 700.h,
        decoration: BoxDecoration(
          color: isDark ? theme.cardBg : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(DesignTokens.radiusXL),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(DesignTokens.radiusXL),
          child: Row(
            children: [
              // Left panel - Checkpoint list
              _buildLeftPanel(theme, isDark),

              // Right panel - Details, create form, or empty state
              _buildRightPanel(theme, isDark),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLeftPanel(ThemeData theme, bool isDark) {
    return Container(
      width: 400.w,
      decoration: BoxDecoration(
        color: isDark
            ? theme.colorScheme.surfaceContainerHighest
            : theme.colorScheme.surface.withOpacity(0.8),
        border: Border(
          right: BorderSide(
            color: isDark
                ? AppTheme.neutralGray700
                : AppTheme.neutralGray300,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: EdgeInsets.all(DesignTokens.spacingL),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: isDark
                      ? AppTheme.neutralGray700
                      : AppTheme.neutralGray300,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.flag,
                  color: theme.colorScheme.primary,
                  size: DesignTokens.iconM,
                ),
                SizedBox(width: DesignTokens.spacingS),
                Expanded(
                  child: Text(
                    'Checkpoints for ${widget.app.name}',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: _refreshCheckpointData,
                  icon: Icon(
                    Icons.refresh,
                    color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                  ),
                  tooltip: 'Refresh checkpoints',
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    Icons.close,
                    color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                  ),
                ),
              ],
            ),
          ),

          // Action button
          Container(
            padding: EdgeInsets.all(DesignTokens.spacingM),
            child: Row(
              children: [
                Expanded(
                  child: PrimaryButton(
                    onPressed: _toggleCreatePanel,
                    icon: _showCreatePanel ? Icons.list : Icons.add,
                    iconPosition: IconPosition.leading,
                    child: Text(_showCreatePanel ? 'View Checkpoints' : 'Create New'),
                  ),
                ),
              ],
            ),
          ),

          // Checkpoint list
          Expanded(
            child: _buildCheckpointsContent(theme, isDark),
          ),
        ],
      ),
    );
  }

  Widget _buildCheckpointsContent(ThemeData theme, bool isDark) {
    final checkpointsAsync = ref.watch(checkpointsForAppProvider(widget.app.id));

    return checkpointsAsync.when(
      data: (checkpoints) => _buildCheckpointsList(context, checkpoints, theme, isDark),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text(
          'Error loading checkpoints: $error',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
          ),
        ),
      ),
    );
  }

  Widget _buildCheckpointsList(BuildContext context, List<CheckpointWithStatus> checkpoints, ThemeData theme, bool isDark) {
    if (checkpoints.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.flag_outlined,
              size: 64.sp,
              color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray400,
            ),
            SizedBox(height: DesignTokens.spacingM),
            Text(
              'No checkpoints yet',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
              ),
            ),
            SizedBox(height: DesignTokens.spacingS),
            Text(
              'Create your first checkpoint using the button above',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark ? AppTheme.neutralGray500 : AppTheme.neutralGray500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Consumer(
      builder: (context, ref, child) {
        final activeCheckpointState = ref.watch(activeCheckpointForAppProvider(widget.app.id));

        return ListView.builder(
          padding: EdgeInsets.all(DesignTokens.spacingM),
          itemCount: checkpoints.length,
          itemBuilder: (context, index) {
            final checkpointWithStatus = checkpoints[index];

            // Compute active status from unified state management
            bool isLoading = activeCheckpointState.isLoading;
            bool isActive;

            // Use optimistic state if available, otherwise use server state
            if (activeCheckpointState.optimisticCheckpointId != null) {
              // We have optimistic state - use it for immediate feedback
              if (activeCheckpointState.optimisticCheckpointId == -1) {
                // Special case: optimistically clearing all checkpoints
                isActive = false;
              } else {
                // Normal case: optimistically activating a specific checkpoint
                isActive = activeCheckpointState.optimisticCheckpointId == checkpointWithStatus.checkpoint.id;
              }
            } else {
              // No optimistic state - use server state
              isActive = activeCheckpointState.activeCheckpoint?.id == checkpointWithStatus.checkpoint.id;
            }

            return _buildCheckpointCard(
              context,
              checkpointWithStatus,
              isActive,
              isLoading,
              theme,
              isDark
            );
          },
        );
      },
    );
  }

  Widget _buildCheckpointCard(
    BuildContext context,
    CheckpointWithStatus checkpointWithStatus,
    bool isActive,
    bool isLoading,
    ThemeData theme,
    bool isDark,
  ) {
    final isSelected = _selectedCheckpoint?.checkpoint.id == checkpointWithStatus.checkpoint.id;

    return GestureDetector(
      onTap: () => _selectCheckpoint(checkpointWithStatus),
      child: Container(
        margin: EdgeInsets.only(bottom: DesignTokens.spacingM),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primary.withOpacity(0.1)
              : (isDark ? theme.cardBg : theme.colorScheme.surface),
          borderRadius: BorderRadius.circular(DesignTokens.radiusL),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: isSelected
                ? theme.colorScheme.primary
                : (isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: ListTile(
          contentPadding: EdgeInsets.all(DesignTokens.spacingM),
          leading: Container(
            width: DesignTokens.iconXL,
            height: DesignTokens.iconXL,
            decoration: BoxDecoration(
              color: Color(int.parse(checkpointWithStatus.color.replaceFirst('#', '0xFF'))),
              shape: BoxShape.circle,
            ),
          ),
          title: Text(
            checkpointWithStatus.name,
            style: theme.textTheme.titleMedium?.copyWith(
              color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (checkpointWithStatus.description != null)
                Text(
                  checkpointWithStatus.description!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              SizedBox(height: DesignTokens.spacingXS),
              Text(
                'Valid from: ${_formatDate(checkpointWithStatus.validFrom)}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isDark ? AppTheme.neutralGray500 : AppTheme.neutralGray500,
                  fontSize: 10.sp,
                ),
              ),
            ],
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Active',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
                      fontSize: 10.sp,
                    ),
                  ),
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      Switch(
                        value: isActive,
                        onChanged: isLoading ? null : (value) {
                          if (value) {
                            ref.read(activeCheckpointForAppProvider(widget.app.id).notifier).setActive(checkpointWithStatus.checkpoint);
                          } else {
                            ref.read(activeCheckpointForAppProvider(widget.app.id).notifier).setInactive();
                          }
                        },
                        activeColor: AppTheme.successColor,
                        inactiveThumbColor: isDark ? AppTheme.neutralGray500 : AppTheme.neutralGray400,
                        inactiveTrackColor: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
                      ),
                      if (isLoading)
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
              SizedBox(width: DesignTokens.spacingS),
              IconButton(
                onPressed: () => _deleteCheckpoint(checkpointWithStatus.checkpoint),
                icon: Icon(
                  Icons.delete_outline,
                  color: AppTheme.errorColor,
                  size: DesignTokens.iconS,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRightPanel(ThemeData theme, bool isDark) {
    return Expanded(
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        switchInCurve: Curves.easeInOut,
        switchOutCurve: Curves.easeInOut,
        child: _showCreatePanel
            ? _buildCreateForm(theme, isDark)
            : (_selectedCheckpoint != null
                ? _buildCheckpointDetails(theme, isDark)
                : _buildEmptyState(theme, isDark)),
      ),
    );
 }

  Widget _buildEmptyState(ThemeData theme, bool isDark) {
    return Container(
      padding: EdgeInsets.all(DesignTokens.spacingXL),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.flag,
            size: 80.sp,
            color: theme.colorScheme.primary.withOpacity(0.3),
          ),
          SizedBox(height: DesignTokens.spacingL),
          Text(
            'Checkpoint Management',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: DesignTokens.spacingM),
          Text(
            'Create and manage checkpoints for ${widget.app.name}.\n\nCheckpoints help you track progress and organize your sessions into meaningful segments.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
              height: 1.6,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: DesignTokens.spacingXL),
          OutlinedModernButton(
            onPressed: _toggleCreatePanel,
            icon: Icons.add,
            iconPosition: IconPosition.leading,
            child: const Text('Create Checkpoint'),
          ),
        ],
      ),
    );
  }

  Widget _buildCheckpointDetails(ThemeData theme, bool isDark) {
    if (_selectedCheckpoint == null) return const SizedBox.shrink();

    final checkpoint = _selectedCheckpoint!;

    return Container(
      padding: EdgeInsets.all(DesignTokens.spacingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with back button
          Row(
            children: [
              IconButton(
                onPressed: () => setState(() => _selectedCheckpoint = null),
                icon: Icon(
                  Icons.arrow_back,
                  color: theme.colorScheme.primary,
                ),
              ),
              SizedBox(width: DesignTokens.spacingS),
              Text(
                'Checkpoint Details',
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: DesignTokens.spacingL),

          // Color indicator
          Container(
            width: DesignTokens.iconXL,
            height: DesignTokens.iconXL,
            decoration: BoxDecoration(
              color: Color(int.parse(checkpoint.color.replaceFirst('#', '0xFF'))),
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(height: DesignTokens.spacingM),

          // Name
          Text(
            checkpoint.name,
            style: theme.textTheme.headlineMedium?.copyWith(
              color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: DesignTokens.spacingS),

          // Description
          if (checkpoint.description != null) ...[
            Text(
              checkpoint.description!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
              ),
            ),
            SizedBox(height: DesignTokens.spacingM),
          ],

          // Valid from date
          Text(
            'Valid From',
            style: theme.textTheme.titleMedium?.copyWith(
              color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: DesignTokens.spacingXS),
          Text(
            _formatDate(checkpoint.validFrom),
            style: theme.textTheme.bodyLarge?.copyWith(
              color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
            ),
          ),
          SizedBox(height: DesignTokens.spacingL),

          // Stats section
          Text(
            'Statistics',
            style: theme.textTheme.titleMedium?.copyWith(
              color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: DesignTokens.spacingM),

          // Stats cards
          Container(
            padding: EdgeInsets.all(DesignTokens.spacingM),
            decoration: BoxDecoration(
              color: isDark ? theme.cardBg : theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(DesignTokens.radiusL),
              border: Border.all(
                color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.timelapse,
                      color: theme.colorScheme.primary,
                      size: DesignTokens.iconM,
                    ),
                    SizedBox(width: DesignTokens.spacingS),
                    Text(
                      'Total Sessions',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${checkpoint.checkpoint.sessionsCount ?? 0}',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: DesignTokens.spacingM),
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      color: theme.colorScheme.primary,
                      size: DesignTokens.iconM,
                    ),
                    SizedBox(width: DesignTokens.spacingS),
                    Text(
                      'Total Time',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
                      ),
                    ),
                    const Spacer(),
                    Text(TimeUtil.formatDurationFromMinutesAsHours(checkpoint.checkpoint.duration ?? 0),
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const Spacer(),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedModernButton(
                  onPressed: () => _deleteCheckpoint(checkpoint.checkpoint),
                  icon: Icons.delete,
                  iconPosition: IconPosition.leading,
                  child: const Text('Delete'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCreateForm(ThemeData theme, bool isDark) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          padding: EdgeInsets.all(DesignTokens.spacingL),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _showCreatePanel = false;
                          _animationController.reverse();
                        });
                      },
                      icon: Icon(
                        Icons.arrow_back,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    SizedBox(width: DesignTokens.spacingS),
                    Text(
                      'Create New Checkpoint',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: DesignTokens.spacingL),
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'Checkpoint Name',
                    hintText: 'e.g., Q1 2024, Project Alpha',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(DesignTokens.radiusL),
                    ),
                    prefixIcon: Icon(Icons.flag),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a checkpoint name';
                    }
                    return null;
                  },
                ),
                SizedBox(height: DesignTokens.spacingM),
                TextFormField(
                  controller: _descriptionController,
                  decoration: InputDecoration(
                    labelText: 'Description (Optional)',
                    hintText: 'Add a description for this checkpoint',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(DesignTokens.radiusL),
                    ),
                    prefixIcon: Icon(Icons.description),
                  ),
                  maxLines: 3,
                ),
                SizedBox(height: DesignTokens.spacingM),
                Text(
                  'Valid From',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: DesignTokens.spacingS),
                InkWell(
                  onTap: () => _selectDate(context),
                  child: Container(
                    padding: EdgeInsets.all(DesignTokens.spacingM),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray300,
                      ),
                      borderRadius: BorderRadius.circular(DesignTokens.radiusL),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.calendar_today),
                        SizedBox(width: DesignTokens.spacingS),
                        Text(_formatDate(_validFrom)),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: DesignTokens.spacingM),
                Text(
                  'Color',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: DesignTokens.spacingS),
                Wrap(
                  spacing: DesignTokens.spacingS,
                  runSpacing: DesignTokens.spacingS,
                  children: _colorOptions.map((color) {
                    final isSelected = color == _selectedColor;
                    return GestureDetector(
                      onTap: () => setState(() => _selectedColor = color),
                      child: Container(
                        width: DesignTokens.iconXL,
                        height: DesignTokens.iconXL,
                        decoration: BoxDecoration(
                          color: Color(int.parse(color.replaceFirst('#', '0xFF'))),
                          shape: BoxShape.circle,
                          border: isSelected
                              ? Border.all(color: theme.primaryColor, width: 3)
                              : null,
                        ),
                        child: isSelected
                            ? Icon(
                                Icons.check,
                                color: Colors.white,
                                size: DesignTokens.iconS,
                              )
                            : null,
                      ),
                    );
                  }).toList(),
                ),
                const Spacer(),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedModernButton(
                        onPressed: () {
                          setState(() {
                            _showCreatePanel = false;
                            _animationController.reverse();
                          });
                        },
                        child: const Text('Cancel'),
                      ),
                    ),
                    SizedBox(width: DesignTokens.spacingM),
                    Expanded(
                      child: PrimaryButton(
                        onPressed: _createCheckpoint,
                        child: const Text('Create Checkpoint'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _createCheckpoint() {
    if (_formKey.currentState?.validate() ?? false) {
      final name = _nameController.text.trim();

      ref.read(checkpointsForAppProvider(widget.app.id).notifier).createCheckpoint(
        name,
        _descriptionController.text.trim().isEmpty ? null : _descriptionController.text.trim(),
        _validFrom,
        _selectedColor,
      );

      // Reset form
      _nameController.clear();
      _descriptionController.clear();
      _selectedColor = '#2196F3';
      _validFrom = DateTime.now();

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Checkpoint "$name" created successfully'),
          backgroundColor: AppTheme.successColor,
          duration: const Duration(seconds: 2),
        ),
      );

      // Go back to list view
      setState(() {
        _showCreatePanel = false;
        _animationController.reverse();
      });
    }
  }

  void _selectDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: _validFrom,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() => _validFrom = date);
    }
  }

  void _deleteCheckpoint(CheckpointModel checkpoint) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        title: Text('Delete Checkpoint'),
        content: Text('Are you sure you want to delete "${checkpoint.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              navigator.pop();

              try {
                await ref.read(checkpointsForAppProvider(widget.app.id).notifier).deleteCheckpoint(checkpoint.id);
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('Checkpoint "${checkpoint.name}" deleted successfully'),
                      backgroundColor: AppTheme.successColor,
                    ),
                  );

                  // Clear selection if deleted checkpoint was selected
                  if (_selectedCheckpoint?.checkpoint.id == checkpoint.id) {
                    setState(() => _selectedCheckpoint = null);
                  }
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete checkpoint: $e'),
                      backgroundColor: AppTheme.errorColor,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}