import 'package:flutter/material.dart';
import '../../../app/design_system/design_system.dart';

class SearchFilterLayout extends StatelessWidget {
  final Widget searchWidget;
  final VoidCallback onFilterPressed;
  final VoidCallback? onClearPressed;
  final bool hasActiveFilters;
  final Widget? filterChips;

  const SearchFilterLayout({
    super.key,
    required this.searchWidget,
    required this.onFilterPressed,
    this.onClearPressed,
    required this.hasActiveFilters,
    this.filterChips,
  });

  @override
  Widget build(BuildContext context) {
    return ModernContainer(
      padding: EdgeInsets.all(DesignTokens.getContentPadding(MediaQuery.of(context).size.width)),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
          child: ModernColumn(
            spacing: DesignTokens.isMobile(MediaQuery.of(context).size.width) ? DesignTokens.spacingS : DesignTokens.spacingM,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ModernRow(
                spacing: DesignTokens.spacingM,
                children: [
                  Expanded(
                    child: searchWidget,
                  ),
                  Stack(
                    children: [
                      SecondaryButton(
                        onPressed: onFilterPressed,
                        icon: Icons.filter_list,
                        size: ButtonSize.medium,
                        child: const Text('Filter'),
                      ),
                      if (hasActiveFilters)
                        Positioned(
                          right: 8,
                          top: 8,
                          child: StatusIndicator(
                            status: StatusType.tracking,
                            size: 8,
                          ),
                        ),
                    ],
                  ),
                  if (hasActiveFilters && onClearPressed != null)
                    OutlinedModernButton(
                      onPressed: onClearPressed!,
                      icon: Icons.clear,
                      size: ButtonSize.medium,
                      child: const Text('Clear'),
                    ),
                ],
              ),
              if (hasActiveFilters && filterChips != null)
                filterChips!,
            ],
          ),
        ),
      ),
    );
  }
}