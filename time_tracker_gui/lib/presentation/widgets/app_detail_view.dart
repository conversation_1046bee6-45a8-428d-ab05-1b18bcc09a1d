import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:async';
import '../../data/models/app_model.dart';
import '../providers/app_timeline_providers.dart';
import '../providers/state_providers.dart';
import '../providers/checkpoint_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../app/design_system/design_system.dart';

// Import the new components
import 'app_detail/app_detail_enums.dart';
import 'app_detail/app_hero_banner.dart';
import 'app_detail/flat_app_stats_row.dart';
import 'app_detail/flat_longest_session_card.dart';
import 'app_detail/flat_total_play_time_card.dart';
import 'app_detail/time_range_selector.dart';
import 'app_detail/checkpoint_selector.dart';
import 'app_detail/checkpoint_stats_card.dart';
import 'app_detail/sessions_section.dart';
import 'app_detail/statistics_section.dart';
import 'checkpoint_dialog.dart';

class AppDetailView extends ConsumerStatefulWidget {
  final AppModel app;

  const AppDetailView({
    super.key,
    required this.app,
  });

  @override
  ConsumerState<AppDetailView> createState() => _AppDetailViewState();
}

class _AppDetailViewState extends ConsumerState<AppDetailView> {
  TimeRange _selectedTimeRange = TimeRange.allTime;
  CheckpointModel? _selectedCheckpoint;
  SessionSortOption _selectedSortOption = SessionSortOption.durationLongest;
  bool _showAllSessions = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTimelineData();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh timeline data when navigating to this view
    _loadTimelineData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _loadTimelineData() {
    // Don't invalidate cache - let the provider handle cache-first loading
    // The provider will show cached data immediately and refresh in background
    final dateRange = _selectedTimeRange.getDateRange();
    final params = AppTimelineParams(
      appId: widget.app.id!,
      startDate: dateRange?.start,
      endDate: dateRange?.end,
    );
    
    // Just access the provider - it will handle cache loading and background refresh
    ref.read(appTimelineProvider(params));
  }

  void _refreshCheckpointData() {
    invalidateCheckpointCache(ref, widget.app.id);

    ref.read(checkpointsForAppProvider(widget.app.id).notifier).refresh();
    ref.read(activeCheckpointForAppProvider(widget.app.id).notifier).refresh();
  }

  void _onTimeRangeChanged(TimeRange newRange) {
    setState(() {
      _selectedTimeRange = newRange;
      _showAllSessions = false;
    });
    _loadTimelineData();
  }

  void _onCheckpointChanged(CheckpointModel? checkpoint) {
    setState(() {
      _selectedCheckpoint = checkpoint;
      _showAllSessions = false;
    });
  }

  void _onSortOptionChanged(SessionSortOption newOption) {
    setState(() => _selectedSortOption = newOption);
  }

  void _onToggleShowAllSessions() {
    setState(() => _showAllSessions = !_showAllSessions);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          AppHeroBanner(
            app: widget.app,
            onEdit: () => _showEditDialog(context),
            onDelete: () => _showDeleteConfirmation(context),
          ),
          SliverToBoxAdapter(
            child: Container(
              padding: EdgeInsets.zero,
              margin: EdgeInsets.zero,
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: DesignTokens.maxContentWidth),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: DesignTokens.spacingM,
                      vertical: DesignTokens.spacingM,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSectionCard(
                          theme,
                          isDark,
                          FlatAppStatsRow(appId: widget.app.id),
                        ),
                        SizedBox(height: DesignTokens.spacingM),
                        LayoutBuilder(
                          builder: (context, constraints) {
                            // On mobile, stack vertically
                            if (constraints.maxWidth < 600) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _buildSectionCard(
                                    theme,
                                    isDark,
                                    FlatLongestSessionCard(appId: widget.app.id),
                                  ),
                                  SizedBox(height: DesignTokens.spacingM),
                                  _buildSectionCard(
                                    theme,
                                    isDark,
                                    FlatTotalPlayTimeCard(appId: widget.app.id),
                                  ),
                                ],
                              );
                            }
                            // On larger screens, place side by side
                            return Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: _buildSectionCard(
                                    theme,
                                    isDark,
                                    FlatLongestSessionCard(appId: widget.app.id),
                                  ),
                                ),
                                SizedBox(width: DesignTokens.spacingM),
                                Expanded(
                                  child: _buildSectionCard(
                                    theme,
                                    isDark,
                                    FlatTotalPlayTimeCard(appId: widget.app.id),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                        SizedBox(height: DesignTokens.spacingM),
                        _buildFiltersSection(theme, isDark),
                        SizedBox(height: DesignTokens.spacingM),
                        if (_selectedCheckpoint != null)
                          _buildSectionCard(
                            theme,
                            isDark,
                            CheckpointStatsCard(
                              checkpoint: _selectedCheckpoint!,
                              appId: widget.app.id.toString(),
                            ),
                          ),
                        SizedBox(height: DesignTokens.spacingM),
                        _buildSectionCard(
                          theme,
                          isDark,
                          SessionsSection(
                            appId: widget.app.id.toString(),
                            selectedTimeRange: _selectedTimeRange,
                            selectedCheckpoint: _selectedCheckpoint,
                            selectedSortOption: _selectedSortOption,
                            showAllSessions: _showAllSessions,
                            onSortChanged: _onSortOptionChanged,
                            onToggleShowAll: _onToggleShowAllSessions,
                          ),
                        ),
                        SizedBox(height: DesignTokens.spacingM),
                        _buildSectionCard(
                          theme,
                          isDark,
                          StatisticsSection(
                            appId: widget.app.id.toString(),
                            selectedTimeRange: _selectedTimeRange,
                            selectedCheckpoint: _selectedCheckpoint,
                          ),
                        ),
                        SizedBox(height: DesignTokens.spacingL),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(ThemeData theme, bool isDark, Widget child) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        border: Border.all(
          color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
          width: 1,
        ),
      ),
      padding: EdgeInsets.all(DesignTokens.spacingL),
      child: child,
    );
  }

  Widget _buildFiltersSection(ThemeData theme, bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? theme.cardBg : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        border: Border.all(
          color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
          width: 1,
        ),
      ),
      padding: EdgeInsets.all(DesignTokens.spacingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filters',
            style: theme.textTheme.titleMedium?.copyWith(
              color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: DesignTokens.spacingM),
          LayoutBuilder(
            builder: (context, constraints) {
              // On mobile, stack filters vertically
              if (constraints.maxWidth < 600) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFilterColumn(
                      theme,
                      isDark,
                      'Time Range',
                      TimeRangeSelector(
                        selectedTimeRange: _selectedTimeRange,
                        onChanged: _onTimeRangeChanged,
                      ),
                    ),
                    SizedBox(height: DesignTokens.spacingL),
                    _buildFilterColumn(
                      theme,
                      isDark,
                      'Checkpoint',
                      CheckpointSelector(
                        app: widget.app,
                        selectedCheckpoint: _selectedCheckpoint,
                        onChanged: _onCheckpointChanged,
                      ),
                    ),
                  ],
                );
              }
              // On larger screens, place side by side
              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: _buildFilterColumn(
                      theme,
                      isDark,
                      'Time Range',
                      TimeRangeSelector(
                        selectedTimeRange: _selectedTimeRange,
                        onChanged: _onTimeRangeChanged,
                      ),
                    ),
                  ),
                  SizedBox(width: DesignTokens.spacingL),
                  Expanded(
                    child: _buildFilterColumn(
                      theme,
                      isDark,
                      'Checkpoint',
                      CheckpointSelector(
                        app: widget.app,
                        selectedCheckpoint: _selectedCheckpoint,
                        onChanged: _onCheckpointChanged,
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFilterColumn(ThemeData theme, bool isDark, String label, Widget selector) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: isDark ? AppTheme.neutralGray400 : AppTheme.neutralGray600,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: DesignTokens.spacingS),
        selector,
      ],
    );
  }

  void _showEditDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CheckpointDialog(app: widget.app),
    );
  }

  void _showDeleteConfirmation(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignTokens.radiusL),
        ),
        child: Container(
          width: 400.w,
          decoration: BoxDecoration(
            color: isDark ? theme.cardBg : theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(DesignTokens.radiusL),
            border: Border.all(
              color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray300,
              width: 1,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(DesignTokens.spacingL),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.error.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.delete_outline,
                        color: theme.colorScheme.error,
                        size: DesignTokens.iconM,
                      ),
                    ),
                    SizedBox(width: DesignTokens.spacingM),
                    Expanded(
                      child: Text(
                        'Delete App',
                        style: theme.textTheme.titleLarge?.copyWith(
                          color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: DesignTokens.spacingM),
                Text(
                  'Are you sure you want to delete "${widget.app.name}"? This action cannot be undone.',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: isDark ? AppTheme.neutralGray300 : AppTheme.neutralGray700,
                  ),
                ),
                SizedBox(height: DesignTokens.spacingL),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(
                          horizontal: DesignTokens.spacingM,
                          vertical: DesignTokens.spacingS,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(DesignTokens.radiusM),
                        ),
                        side: BorderSide(
                          color: isDark ? AppTheme.neutralGray600 : AppTheme.neutralGray400,
                        ),
                      ),
                      child: Text(
                        'Cancel',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                        ),
                      ),
                    ),
                    SizedBox(width: DesignTokens.spacingM),
                    ElevatedButton(
                      onPressed: () {
                        final appName = widget.app.name;
                        if (appName != null && appName.isNotEmpty) {
                          ref.read(appsProvider.notifier).deleteApp(appName);
                          Navigator.of(context).pop();
                          Navigator.of(context).pop();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.error,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          horizontal: DesignTokens.spacingM,
                          vertical: DesignTokens.spacingS,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(DesignTokens.radiusM),
                        ),
                      ),
                      child: Text(
                        'Delete',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
