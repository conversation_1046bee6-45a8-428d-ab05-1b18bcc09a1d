import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_riverpod/legacy.dart';
import '../../data/models/app_model.dart';
import '../../data/repositories/time_tracker_repository.dart';
import 'service_providers.dart';

import 'dart:convert';
import 'package:flutter_riverpod/experimental/persist.dart';
import 'package:riverpod_sqflite/riverpod_sqflite.dart';
import 'storage_provider.dart';

const bool _kProvidersDebug = true;
void _d(String msg) {
  if (_kProvidersDebug) {
    // ignore: avoid_print
    print('[state_providers] $msg');
  }
}

bool _isWsRepo(TimeTrackerRepository r) => r is WebSocketTimeTrackerRepository;
WebSocketTimeTrackerRepository? _asWs(TimeTrackerRepository r) =>
    r is WebSocketTimeTrackerRepository ? r : null;

void _setLoadingIfNoValue<T>(AsyncValue<T> state, void Function(AsyncValue<T>) setState) {
  if (!state.hasValue) setState(const AsyncValue.loading());
}

List<TimelineModel> _mergeTimelineEntries(List<TimelineModel> existing, List<TimelineModel> newEntries) {
  final existingMap = <String, TimelineModel>{};
  for (final entry in existing) {
    final key = entry.id?.toString() ??
      '${entry.date?.millisecondsSinceEpoch}_${entry.duration}_${entry.appId}';
    existingMap[key] = entry;
  }

  for (final newEntry in newEntries) {
    final key = newEntry.id?.toString() ??
      '${newEntry.date?.millisecondsSinceEpoch}_${newEntry.duration}_${newEntry.appId}';
    final existingEntry = existingMap[key];

    if (existingEntry != null) {
      final shouldPreserveCheckpointId =
        newEntry.checkpointId == null && existingEntry.checkpointId != null;
      final shouldPreserveAssociations =
        newEntry.checkpointAssociations.isEmpty && existingEntry.checkpointAssociations.isNotEmpty;

      existingMap[key] = TimelineModel(
        id: newEntry.id ?? existingEntry.id,
        date: newEntry.date ?? existingEntry.date,
        duration: newEntry.duration ?? existingEntry.duration,
        appId: newEntry.appId,
        checkpointId: shouldPreserveCheckpointId ? existingEntry.checkpointId : newEntry.checkpointId,
        checkpointAssociations: shouldPreserveAssociations ?
          existingEntry.checkpointAssociations : newEntry.checkpointAssociations,
      );
    } else {
      existingMap[key] = newEntry;
    }
  }

  final mergedList = existingMap.values.toList();
  mergedList.sort((a, b) {
    final dateA = a.date;
    final dateB = b.date;

    if (dateA == null && dateB == null) return 0;
    if (dateA == null) return 1;
    if (dateB == null) return -1;

    return dateB.compareTo(dateA);
  });

  return mergedList;
}

// ---------- State Providers ----------

final appsProvider = AsyncNotifierProvider<AppsNotifier, List<AppModel>>(
  AppsNotifier.new,
);

final trackingStatusProvider = AsyncNotifierProvider<TrackingStatusNotifier, TrackingStatus>(
  TrackingStatusNotifier.new,
);

final statisticsProvider = AsyncNotifierProvider<StatisticsNotifier, List<AppStatistics>>(
  StatisticsNotifier.new,
);

final timelineProvider = AsyncNotifierProvider<TimelineNotifier, List<TimelineModel>>(
  TimelineNotifier.new,
);

final sessionCountsProvider = AsyncNotifierProvider<SessionCountsNotifier, Map<int, int>>(
  SessionCountsNotifier.new,
);

// ---------- State Notifiers ----------

class AppsNotifier extends AsyncNotifier<List<AppModel>> {
  late TimeTrackerRepository _repository;

  @override
  Future<List<AppModel>> build() async {
    _repository = ref.watch(timeTrackerRepositoryProvider);
    
    // Set up persistence - Riverpod will handle restoring cached data
    persist(
      ref.watch(storageProvider.future),
      key: 'apps_list',
      options: const StorageOptions(cacheTime: StorageCacheTime(Duration(days: 7))),
      encode: (apps) {
        print('AppsNotifier: Encoding ${apps.length} apps to cache');
        return jsonEncode(apps.map((app) => app.toJson()).toList());
      },
      decode: (json) {
        final List<dynamic> appsList = jsonDecode(json as String) as List<dynamic>;
        print('AppsNotifier: Decoding ${appsList.length} apps from cache');
        return appsList
            .map((e) => AppModel.fromJson(e as Map<String, dynamic>))
            .toList();
      },
    );
    
    // Get cached data first and return it immediately
    final cachedApps = await _getCachedApps();
    
    // Start background initialization and refresh without awaiting (don't block UI)
    _backgroundInitAndRefresh();
    
    // Return cached data immediately if available, otherwise empty list
    // Fresh data will come via streams and update the state
    return cachedApps.isNotEmpty ? cachedApps : <AppModel>[];
  }

  Future<void> _backgroundInitAndRefresh() async {
    // Run initialization and refresh in background without blocking the build method
    try {
      print('AppsNotifier: Starting background initialization and refresh...');
      _listenToAppUpdates();
      _listenToAppsListUpdates();
      // Try to load fresh data (repository will handle connection internally)
      await _loadApps();
    } catch (e) {
      print('AppsNotifier: Background init and refresh failed: $e');
      // Background failure is fine - we already returned cached data
    }
  }


  Future<List<AppModel>> _getCachedApps() async {
    try {
      final storage = await ref.read(storageProvider.future);
      final cachedResult = await storage.read('apps_list');
      if (cachedResult != null && cachedResult.data.isNotEmpty) {
        final List<dynamic> appsList = jsonDecode(cachedResult.data as String) as List<dynamic>;
        final cachedApps = appsList
            .map((e) => AppModel.fromJson(e as Map<String, dynamic>))
            .toList();
        print('AppsNotifier: Returning ${cachedApps.length} cached apps');
        return cachedApps;
      } else {
        print('AppsNotifier: No cached apps found');
        return <AppModel>[];
      }
    } catch (e) {
      print('AppsNotifier: Error reading cached apps: $e');
      return <AppModel>[];
    }
  }

  Future<List<AppModel>> _loadApps() async {
    if (_isWsRepo(_repository)) {
      final ws = _asWs(_repository)!;
      await ws.getAllApps();
      print('AppsNotifier: Requested apps data, will come through streams');
      // Return empty list - fresh data will come through stream updates
      return <AppModel>[];
    } else {
      print('AppsNotifier: Using direct repository connection');
      return await _repository.getAllApps();
    }
  }

  Future<void> loadApps() async {
    try {
      final apps = await _loadApps();
      state = AsyncData(apps);
    } catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
    }
  }

  Future<void> addApp(String name) async {
    try {
      await _repository.insertApp(name);
      if (!_isWsRepo(_repository)) {
        await loadApps();
      }
    } catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
    }
  }

  Future<void> deleteApp(String processName) async {
    try {
      await _repository.deleteApp(processName);
      if (!_isWsRepo(_repository)) {
        await loadApps();
      }
    } catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
    }
  }

  void _listenToAppUpdates() {
    _repository.appUpdateStream.listen((updatedApp) {
      state.whenData((apps) {
        final updated = apps
            .map((a) => a.id == updatedApp.id ? updatedApp : a)
            .toList(growable: false);
        state = AsyncData(updated);
      });
    });
  }

  void _listenToAppsListUpdates() {
    final ws = _asWs(_repository);
    if (ws == null) return;

    ws.appsListStream.listen(
      (apps) {
        // Only update state if we have actual apps data
        // This prevents overwriting cached data with empty lists when offline
        if (apps.isNotEmpty) {
          print('AppsNotifier: Received ${apps.length} apps from stream, updating state');
          state = AsyncData(apps);
        } else {
          print('AppsNotifier: Received empty apps list from stream, keeping current state');
        }
      },
      onError: (error) {
        _d('AppsNotifier: Error in apps list stream: $error (keeping cached state)');
      },
      onDone: () => _d('AppsNotifier: Apps list stream closed'),
    );
  }
}

class TrackingStatusNotifier extends AsyncNotifier<TrackingStatus> {
  late TimeTrackerRepository _repository;

  @override
  Future<TrackingStatus> build() async {
    _repository = ref.watch(timeTrackerRepositoryProvider);
    
    // Set up persistence - Riverpod will handle restoring cached data
    persist(
      ref.watch(storageProvider.future),
      key: 'tracking_status',
      options: const StorageOptions(cacheTime: StorageCacheTime(Duration(minutes: 5))),
      encode: (status) {
        print('TrackingStatusNotifier: Encoding status to cache');
        return jsonEncode(status.toJson());
      },
      decode: (json) {
        final statusData = jsonDecode(json as String) as Map<String, dynamic>;
        final status = TrackingStatus.fromJson(statusData);
        print('TrackingStatusNotifier: Decoding status from cache: isTracking=${status.isTracking}, currentApp=${status.currentApp}');
        return status;
      },
    );
    
    _initializeRepository();
    _listenToStatusUpdates();
    
    // Try to load fresh data
    try {
      final freshStatus = await _loadTrackingStatus();
      return freshStatus;
    } catch (error) {
      print('TrackingStatusNotifier: Failed to load fresh data: $error');
      print('TrackingStatusNotifier: Persistence will restore cached data if available');
      // Let persistence handle cached data restoration
      throw error;
    }
  }

  Future<void> _initializeRepository() async {
    try {
      await _repository.connect();
      if (_isWsRepo(_repository)) {
        await Future.delayed(const Duration(milliseconds: 500));
      }
    } catch (e) {
      _d('TrackingStatusNotifier._initializeRepository failed: $e');
    }
  }

  Future<TrackingStatus> _loadTrackingStatus() async {
    final status = await _repository.getTrackingStatus();
    return status;
  }

  Future<void> loadTrackingStatus() async {
    try {
      final status = await _loadTrackingStatus();
      state = AsyncData(status);
    } catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
    }
  }

  Future<void> startTracking() async {
    try {
      await _repository.startTracking();
    } catch (error) {
      _d('TrackingStatusNotifier.startTracking failed: $error');
    }
  }

  Future<void> stopTracking() async {
    try {
      await _repository.stopTracking();
    } catch (error) {
      _d('TrackingStatusNotifier.stopTracking failed: $error');
    }
  }

  Future<void> pauseTracking() async {
    try {
      await _repository.pauseTracking();
    } catch (error) {
      _d('TrackingStatusNotifier.pauseTracking failed: $error');
    }
  }

  Future<void> resumeTracking() async {
    try {
      await _repository.resumeTracking();
    } catch (error) {
      _d('TrackingStatusNotifier.resumeTracking failed: $error');
    }
  }

  void _listenToStatusUpdates() {
    _repository.trackingStatusStream.listen((status) {
      state = AsyncData(status);
    });
  }
}

class StatisticsNotifier extends AsyncNotifier<List<AppStatistics>> {
  late TimeTrackerRepository _repository;

  @override
  Future<List<AppStatistics>> build() async {
    _repository = ref.watch(timeTrackerRepositoryProvider);
    
    persist(
      ref.watch(storageProvider.future),
      key: 'app_statistics',
      options: const StorageOptions(cacheTime: StorageCacheTime(Duration(hours: 1))),
      encode: (stats) {
        print('StatisticsNotifier: Encoding ${stats.length} statistics to cache');
        return jsonEncode(stats.map((stat) => stat.toJson()).toList());
      },
      decode: (json) {
        final decoded = jsonDecode(json as String) as List;
        final statistics = decoded
            .map((e) => AppStatistics.fromJson(e as Map<String, dynamic>))
            .toList();
        print('StatisticsNotifier: Decoding ${statistics.length} statistics from cache');
        return statistics;
      },
    );
    
    // Get cached data first and return it immediately
    final cachedStats = await _getCachedStatistics();
    
    // Start background refresh without awaiting (don't block UI)
    _backgroundRefresh();
    
    // Return cached data immediately if available, otherwise empty list
    // Fresh data will come via streams and update the state
    return cachedStats.isNotEmpty ? cachedStats : <AppStatistics>[];
  }


  Future<List<AppStatistics>> _getCachedStatistics() async {
    try {
      final storage = await ref.read(storageProvider.future);
      final cachedResult = await storage.read('app_statistics');
      if (cachedResult != null && cachedResult.data.isNotEmpty) {
        final decoded = jsonDecode(cachedResult.data as String) as List;
        final cachedStats = decoded
            .map((e) => AppStatistics.fromJson(e as Map<String, dynamic>))
            .toList();
        print('StatisticsNotifier: Returning ${cachedStats.length} cached statistics');
        return cachedStats;
      } else {
        print('StatisticsNotifier: No cached statistics found');
        return <AppStatistics>[];
      }
    } catch (e) {
      print('StatisticsNotifier: Error reading cached statistics: $e');
      return <AppStatistics>[];
    }
  }

  Future<void> _backgroundRefresh() async {
    // Run refresh in background without blocking the build method
    try {
      print('StatisticsNotifier: Starting background refresh...');
      _listenToStatisticsUpdates();
      // Repository will handle connection internally
      await _loadStatistics();
    } catch (e) {
      print('StatisticsNotifier: Background refresh failed: $e');
      // Background failure is fine - we already returned cached data
    }
  }

  Future<List<AppStatistics>> _loadStatistics({DateTime? startDate, DateTime? endDate}) async {
    try {
      if (_isWsRepo(_repository)) {
        // For WebSocket, just request data - it will come through streams
        await _repository.getStatistics(
            startDate: startDate, endDate: endDate);
        return <AppStatistics>[];
      } else {
        return await _repository.getStatistics(
            startDate: startDate, endDate: endDate);
      }
    } catch (error) {
      _d('StatisticsNotifier._loadStatistics failed: $error');
      return <AppStatistics>[];
    }
  }

  Future<void> loadStatistics({DateTime? startDate, DateTime? endDate}) async {
    try {
      final statistics = await _loadStatistics(startDate: startDate, endDate: endDate);
      state = AsyncData(statistics);
    } catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
    }
  }

  void _listenToStatisticsUpdates() {
    final ws = _asWs(_repository);
    if (ws == null) return;
    ws.statisticsStream.listen((statistics) {
      // Only update state if we have actual statistics data
      // This prevents overwriting cached data with empty lists when offline
      if (statistics.isNotEmpty) {
        print('StatisticsNotifier: Received ${statistics.length} statistics from stream, updating state');
        state = AsyncData(statistics);
      } else {
        print('StatisticsNotifier: Received empty statistics from stream, keeping current state');
      }
    });
  }
}

class TimelineNotifier extends AsyncNotifier<List<TimelineModel>> {
  late TimeTrackerRepository _repository;

  @override
  Future<List<TimelineModel>> build() async {
    _repository = ref.watch(timeTrackerRepositoryProvider);
    
    await persist(
      ref.watch(storageProvider.future),
      key: 'timeline_data',
      options: const StorageOptions(cacheTime: StorageCacheTime(Duration(hours: 1))),
      encode: (timeline) => jsonEncode(timeline.map((entry) => entry.toJson()).toList()),
      decode: (json) {
        final decoded = jsonDecode(json as String) as List;
        return decoded
            .map((e) => TimelineModel.fromJson(e as Map<String, dynamic>))
            .toList();
      },
    ).future;
    
    _initializeRepository();
    _listenToTimelineUpdates();
    
    return await _loadTimeline();
  }

  Future<void> _initializeRepository() async {
    try {
      if (_isWsRepo(_repository)) {
        final ws = _asWs(_repository)!;
        await ws.connect();
        await Future.delayed(const Duration(milliseconds: 500));
      }
    } catch (e) {
      _d('TimelineNotifier._initializeRepository failed: $e');
    }
  }

  Future<List<TimelineModel>> _loadTimeline({
    DateTime? startDate,
    DateTime? endDate,
    int? appId,
  }) async {
    try {
      if (_isWsRepo(_repository)) {
        await _repository.getTimeline(
            startDate: startDate, endDate: endDate, appId: appId);
        // Return empty list for now, data will come through streams
        return <TimelineModel>[];
      } else {
        return await _repository.getTimeline(
            startDate: startDate, endDate: endDate, appId: appId);
      }
    } catch (error) {
      _d('TimelineNotifier._loadTimeline failed: $error');
      return <TimelineModel>[];
    }
  }

  Future<void> loadTimeline({
    DateTime? startDate,
    DateTime? endDate,
    int? appId,
  }) async {
    try {
      final timeline = await _loadTimeline(
          startDate: startDate, endDate: endDate, appId: appId);
      state = AsyncData(timeline);
    } catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
    }
  }

  void _listenToTimelineUpdates() {
    final ws = _asWs(_repository);
    if (ws == null) return;
    ws.timelineStream.listen((timeline) {
      final existingData = state.when(
        data: (data) => data,
        loading: () => <TimelineModel>[],
        error: (_, __) => <TimelineModel>[],
      );
      final mergedTimeline = _mergeTimelineEntries(existingData, timeline);
      state = AsyncData(mergedTimeline);
    });
  }
}

class SessionCountsNotifier extends AsyncNotifier<Map<int, int>> {
  late TimeTrackerRepository _repository;

  @override
  Future<Map<int, int>> build() async {
    _repository = ref.watch(timeTrackerRepositoryProvider);
    
    // Set up persistence - Riverpod will automatically restore cached data if available
    persist(
      ref.watch(storageProvider.future),
      key: 'session_counts',
      options: const StorageOptions(cacheTime: StorageCacheTime(Duration(hours: 1))),
      encode: (counts) {
        print('SessionCountsNotifier: Encoding ${counts.length} counts to cache');
        return jsonEncode(counts.map((key, value) => MapEntry(key.toString(), value)));
      },
      decode: (json) {
        final decoded = jsonDecode(json as String) as Map<String, dynamic>;
        final counts = decoded.map((key, value) => MapEntry(int.parse(key), value as int));
        print('SessionCountsNotifier: Decoding ${counts.length} counts from cache');
        return counts;
      },
    );
    
    // Get cached data first and return it immediately
    final cachedCounts = await _getCachedSessionCounts();
    
    // Start background refresh without awaiting (don't block UI)
    _backgroundRefresh();
    
    // Return cached data immediately if available, otherwise empty map
    // Fresh data will come via streams and update the state
    return cachedCounts.isNotEmpty ? cachedCounts : <int, int>{};
  }



  Future<Map<int, int>> _getCachedSessionCounts() async {
    try {
      final storage = await ref.read(storageProvider.future);
      final cachedResult = await storage.read('session_counts');
      if (cachedResult != null && cachedResult.data.isNotEmpty) {
        final decoded = jsonDecode(cachedResult.data as String) as Map<String, dynamic>;
        final cachedCounts = decoded.map((key, value) => MapEntry(int.parse(key), value as int));
        print('SessionCountsNotifier: Returning ${cachedCounts.length} cached session counts');
        return cachedCounts;
      } else {
        print('SessionCountsNotifier: No cached session counts found');
        return <int, int>{};
      }
    } catch (e) {
      print('SessionCountsNotifier: Error reading cached session counts: $e');
      return <int, int>{};
    }
  }

  Future<void> _backgroundRefresh() async {
    // Run refresh in background without blocking the build method
    try {
      print('SessionCountsNotifier: Starting background refresh...');
      _listenToSessionCountsUpdates();
      // Repository will handle connection internally
      await _loadSessionCounts();
    } catch (e) {
      print('SessionCountsNotifier: Background refresh failed: $e');
      // Background failure is fine - we already returned cached data
    }
  }

  Future<Map<int, int>> _loadSessionCounts() async {
    try {
      if (_isWsRepo(_repository)) {
        // For WebSocket, just request data - it will come through streams
        await _repository.getSessionCounts();
        return <int, int>{};
      } else {
        return await _repository.getSessionCounts();
      }
    } catch (error) {
      _d('SessionCountsNotifier._loadSessionCounts failed: $error');
      return <int, int>{};
    }
  }

  Future<void> loadSessionCounts() async {
    try {
      final counts = await _loadSessionCounts();
      state = AsyncData(counts);
    } catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
    }
  }

  Future<void> refresh() async {
    await loadSessionCounts();
  }

  void _listenToSessionCountsUpdates() {
    final ws = _asWs(_repository);
    if (ws == null) return;
    ws.sessionCountsStream.listen((counts) {
      // Only update state if we have actual session counts data
      // This prevents overwriting cached data with empty maps when offline
      if (counts.isNotEmpty) {
        print('SessionCountsNotifier: Received ${counts.length} session counts from stream, updating state');
        state = AsyncData(counts);
      } else {
        print('SessionCountsNotifier: Received empty session counts from stream, keeping current state');
      }
    });
  }
}
