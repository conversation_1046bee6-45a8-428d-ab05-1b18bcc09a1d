import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:riverpod_sqflite/riverpod_sqflite.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

/// Provider for the JSON SQLite storage instance
/// This storage will be shared across all providers that need offline persistence
final storageProvider = FutureProvider<JsonSqFliteStorage>((ref) async {
  try {
    // Get the appropriate database path based on platform
    String databasePath;
    if (Platform.isAndroid || Platform.isIOS) {
      // Use the standard sqflite database path for mobile platforms
      databasePath = join(await getDatabasesPath(), 'time_tracker_riverpod.db');
    } else {
      // Use application support directory for desktop platforms
      final appSupportDir = await getApplicationDocumentsDirectory();
      databasePath = join(appSupportDir.path, 'time_tracker_riverpod.db');
    }

    print('Initializing database at: $databasePath');
    return await JsonSqFliteStorage.open(databasePath);
  } catch (error) {
    print('Error initializing storage: $error');
    rethrow;
  }
});
