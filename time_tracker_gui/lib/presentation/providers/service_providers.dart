import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/time_tracker_repository.dart';
import '../../data/services/websocket_service.dart';
import 'settings_provider.dart';

const bool _kProvidersDebug = true;
void _d(String msg) {
  if (_kProvidersDebug) {
    // ignore: avoid_print
    print('[service_providers] $msg');
  }
}

bool _isWsRepo(TimeTrackerRepository r) => r is WebSocketTimeTrackerRepository;
WebSocketTimeTrackerRepository? _asWs(TimeTrackerRepository r) =>
    r is WebSocketTimeTrackerRepository ? r : null;

// ---------- Service Providers ----------

final webSocketServiceProvider = Provider<WebSocketService>((ref) {
  final settings = ref.watch(settingsProvider);
  final wsUrl = settings.backendUrl.replaceFirst('http', 'ws') + '/ws';
  _d('Creating WebSocketService with wsUrl: $wsUrl');

  final webSocketService = WebSocketService(wsUrl: wsUrl);

  ref.onDispose(() {
    _d('Disposing WebSocketService');
    webSocketService.dispose();
  });

  return webSocketService;
});

final timeTrackerRepositoryProvider = Provider<TimeTrackerRepository>((ref) {
  final webSocketService = ref.watch(webSocketServiceProvider);
  _d('Creating WebSocketTimeTrackerRepository');
  final repository = WebSocketTimeTrackerRepository(webSocketService: webSocketService);

  ref.onDispose(() {
    _d('Disposing WebSocketTimeTrackerRepository');
    repository.dispose();
  });

  return repository;
});

final settingsChangeProvider = Provider<void>((ref) {
  ref.listen<AppSettings>(settingsProvider, (previous, next) {
    if (previous != null && previous.backendUrl != next.backendUrl) {
      _d('Settings changed: ${previous.backendUrl} -> ${next.backendUrl}');
      ref.invalidate(webSocketServiceProvider);
      ref.invalidate(timeTrackerRepositoryProvider);
    }
  });
});
