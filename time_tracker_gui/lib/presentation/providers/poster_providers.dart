import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_riverpod/legacy.dart';
import '../../data/services/poster_service.dart';
import '../../data/models/poster_model.dart';

final posterServiceProvider = Provider<PosterService>((ref) {
  return PosterService();
});

final postersProvider = FutureProvider<List<Poster>>((ref) async {
  final posterService = ref.read(posterServiceProvider);
  return posterService.fetchRandomPosters();
});

final searchQueryProvider = StateProvider<String>((ref) => '');

final searchResultsProvider = FutureProvider<List<Poster>>((ref) async {
  final query = ref.watch(searchQueryProvider);
  final posterService = ref.read(posterServiceProvider);

  if (query.trim().isEmpty) {
    return posterService.fetchPosters();
  }

  return posterService.searchGames(query);
});

final currentPageProvider = StateProvider<int>((ref) => 1);