import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/services/daily_session_service.dart';
import '../../data/models/app_model.dart';
import 'state_providers.dart';

import 'dart:convert';
import 'package:flutter_riverpod/experimental/persist.dart';
import 'package:riverpod_sqflite/riverpod_sqflite.dart';
import 'storage_provider.dart';

final todaySessionProvider = AsyncNotifierProvider<TodaySessionNotifier, DailySessionData>(
  TodaySessionNotifier.new,
);

class TodaySessionNotifier extends AsyncNotifier<DailySessionData> {
  @override
  Future<DailySessionData> build() async {
    try {
      await persist(
        ref.watch(storageProvider.future),
        key: 'today_session_data',
        options: const StorageOptions(cacheTime: StorageCacheTime(Duration(days: 7))),
        encode: (data) {
          try {
            return jsonEncode(data.toJson());
          } catch (e) {
            print('Error encoding today_session_data: $e');
            rethrow;
          }
        },
        decode: (json) {
          try {
            final decoded = jsonDecode(json) as Map<String, dynamic>;
            return DailySessionData.fromJson(decoded);
          } catch (e) {
            print('Error decoding today_session_data: $e');
            rethrow;
          }
        },
      ).future;
    } catch (e) {
      print('Error in today_session_data persistence: $e');
      // Continue without persistence on error
    }

    _listenToTrackingUpdates();
    return await loadTodayData();
  }

  void _listenToTrackingUpdates() {
    ref.listen(trackingStatusProvider, (previous, next) {
      next.whenData((current) {
        if (previous != null && previous.hasValue) {
          final prev = previous.value!;
          _handleTrackingStatusChange(prev, current);
        }
      });
    });
  }

  void _handleTrackingStatusChange(TrackingStatus previous, TrackingStatus current) {
    final minutes = current.currentSessionDuration - previous.currentSessionDuration;

    addSession(
      appName: previous.currentApp!,
      durationMinutes: minutes,
      startTime: previous.sessionStartTime ?? DateTime.now(),
    );
  }

  Future<DailySessionData> loadTodayData() async {
    try {
      final data = await DailySessionService.getTodaySessionData();
      return data;
    } catch (error) {
      // If there's an error, return empty data for today
      final today = DateTime.now();
      final todayKey = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
      return DailySessionData(
        date: todayKey,
        totalMinutes: 0,
        sessions: [],
      );
    }
  }

  Future<void> addSession({
    required String appName,
    required int durationMinutes,
    required DateTime startTime,
  }) async {
    if (durationMinutes <= 0) return;

    try {
      await DailySessionService.addSession(
        appName: appName,
        durationMinutes: durationMinutes,
        startTime: startTime,
      );
      
      // When modifying the state, no need for any extra logic to persist the change.
      // Riverpod will automatically cache the new state and write it to the DB.
      final updatedData = await loadTodayData();
      state = AsyncData(updatedData);
    } catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
    }
  }

  Future<void> refresh() async {
    try {
      final updatedData = await loadTodayData();
      state = AsyncData(updatedData);
    } catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
    }
  }

  Future<void> clearAllData() async {
    try {
      await DailySessionService.clearAllData();
      final updatedData = await loadTodayData();
      state = AsyncData(updatedData);
    } catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
    }
  }
}