import 'dart:async';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_riverpod/legacy.dart';
import 'package:flutter_riverpod/experimental/persist.dart';
import 'package:riverpod_sqflite/riverpod_sqflite.dart';
import '../../data/models/app_model.dart';
import '../../data/repositories/time_tracker_repository.dart';
import 'service_providers.dart';
import 'storage_provider.dart';

const bool _kDebug = true;
void _log(String msg) {
  if (_kDebug) {
    print('[app_timeline_providers] $msg');
  }
}

WebSocketTimeTrackerRepository? _asWebSocket(TimeTrackerRepository r) =>
    r is WebSocketTimeTrackerRepository ? r : null;

List<TimelineModel> _mergeTimelineEntries(List<TimelineModel> existing, List<TimelineModel> newEntries) {
  final existingMap = <String, TimelineModel>{};
  for (final entry in existing) {
    final key = entry.id?.toString() ?? '${entry.date?.millisecondsSinceEpoch}_${entry.duration}_${entry.appId}';
    existingMap[key] = entry;
  }

  for (final newEntry in newEntries) {
    final key = newEntry.id?.toString() ?? '${newEntry.date?.millisecondsSinceEpoch}_${newEntry.duration}_${newEntry.appId}';
    final existingEntry = existingMap[key];

    if (existingEntry != null) {
      final shouldPreserveCheckpointId = newEntry.checkpointId == null && existingEntry.checkpointId != null;
      final shouldPreserveAssociations = newEntry.checkpointAssociations.isEmpty && existingEntry.checkpointAssociations.isNotEmpty;

      existingMap[key] = TimelineModel(
        id: newEntry.id ?? existingEntry.id,
        date: newEntry.date ?? existingEntry.date,
        duration: newEntry.duration ?? existingEntry.duration,
        appId: newEntry.appId,
        checkpointId: shouldPreserveCheckpointId ? existingEntry.checkpointId : newEntry.checkpointId,
        checkpointAssociations: shouldPreserveAssociations ? existingEntry.checkpointAssociations : newEntry.checkpointAssociations,
      );
    } else {
      existingMap[key] = newEntry;
    }
  }

  final mergedList = existingMap.values.toList();
  mergedList.sort((a, b) {
    final dateA = a.date;
    final dateB = b.date;

    if (dateA == null && dateB == null) return 0;
    if (dateA == null) return 1;
    if (dateB == null) return -1;

    return dateB.compareTo(dateA);
  });

  return mergedList;
}

// ---------- App Timeline ----------

class AppTimelineParams {
  final int appId;
  final DateTime? startDate;
  final DateTime? endDate;

  const AppTimelineParams({
    required this.appId,
    this.startDate,
    this.endDate,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppTimelineParams &&
        other.appId == appId &&
        other.startDate == startDate &&
        other.endDate == endDate;
  }

  @override
  int get hashCode => Object.hash(appId, startDate, endDate);
}

final appTimelineProvider = StateNotifierProvider.family<AppTimelineNotifier, AsyncValue<List<TimelineModel>>, AppTimelineParams>(
  (ref, params) => AppTimelineNotifier(ref, params),
);

class AppTimelineNotifier extends StateNotifier<AsyncValue<List<TimelineModel>>> {
  final Ref _ref;
  final AppTimelineParams _params;
  late WebSocketTimeTrackerRepository _repository;
  StreamSubscription<List<TimelineModel>>? _timelineSub;
  StreamSubscription<bool>? _connSub;

  AppTimelineNotifier(this._ref, this._params) : super(const AsyncValue.loading()) {
    _initialize();
  }

  Future<void> _initialize() async {
    _repository = _asWebSocket(_ref.read(timeTrackerRepositoryProvider))!;

    // Listen for repository changes
    _ref.listen<TimeTrackerRepository>(timeTrackerRepositoryProvider, (prev, next) {
      final newRepo = _asWebSocket(next);
      if (newRepo != null && !identical(newRepo, _repository)) {
        _updateRepository(newRepo);
      }
    });

    // Load cached data first
    final cachedData = await _getCachedTimeline();
    if (cachedData.isNotEmpty && mounted) {
      state = AsyncValue.data(cachedData);
      _log('Loaded ${cachedData.length} cached entries for app ${_params.appId}');
    }

    // Set up streams and background refresh
    _setupStreams();
  }

  Future<List<TimelineModel>> _getCachedTimeline() async {
    try {
      final storage = await _ref.read(storageProvider.future);

      // First try app-specific cache
      final cacheKey = _getCacheKey();
      final appCacheResult = await storage.read(cacheKey);
      if (appCacheResult?.data != null) {
        final decoded = jsonDecode(appCacheResult!.data as String) as List;
        final timeline = decoded.map((e) => TimelineModel.fromJson(e)).toList();
        final filtered = _filterTimeline(timeline);
        _log('Found ${filtered.length} entries in app-specific cache for ${_params.appId}');
        return filtered;
      }

      // Fallback to all-time cache and filter
      final allTimeResult = await storage.read('all_time_timeline');
      if (allTimeResult?.data != null) {
        final decoded = jsonDecode(allTimeResult!.data as String) as List;
        final allTimeline = decoded.map((e) => TimelineModel.fromJson(e)).toList();
        final filtered = _filterTimeline(allTimeline);
        _log('Extracted ${filtered.length} entries from all-time cache for app ${_params.appId}');
        return filtered;
      }

      _log('No cached data found for app ${_params.appId}');
      return <TimelineModel>[];
    } catch (e) {
      _log('Error reading cache for app ${_params.appId}: $e');
      return <TimelineModel>[];
    }
  }

  String _getCacheKey() {
    final start = _params.startDate?.millisecondsSinceEpoch.toString() ?? 'null';
    final end = _params.endDate?.millisecondsSinceEpoch.toString() ?? 'null';
    return 'app_timeline_${_params.appId}_${start}_$end';
  }

  List<TimelineModel> _filterTimeline(List<TimelineModel> timeline) {
    return timeline.where((entry) {
      // Filter by app ID
      if (entry.appId != _params.appId) return false;

      // Filter by date range
      final date = entry.date;
      if (date == null) return true;
      if (_params.startDate == null && _params.endDate == null) return true;

      final normalizedDate = DateTime(date.year, date.month, date.day);
      final afterStart = _params.startDate == null ||
          normalizedDate.isAfter(_params.startDate!.subtract(const Duration(days: 1)));
      final beforeEnd = _params.endDate == null ||
          normalizedDate.isBefore(_params.endDate!.add(const Duration(days: 1)));

      return afterStart && beforeEnd;
    }).toList()..sort((a, b) {
      final dateA = a.date;
      final dateB = b.date;
      if (dateA == null && dateB == null) return 0;
      if (dateA == null) return 1;
      if (dateB == null) return -1;
      return dateB.compareTo(dateA);
    });
  }

  void _setupStreams() {
    _timelineSub = _repository.timelineStream.listen(
      (timeline) {
        final filtered = _filterTimeline(timeline);
        if (filtered.isNotEmpty) {
          final existing = state.when(
            data: (data) => data,
            loading: () => <TimelineModel>[],
            error: (_, __) => <TimelineModel>[],
          );
          final merged = _mergeTimelineEntries(existing, filtered);
          _log('Updated app ${_params.appId} timeline with ${merged.length} entries');
          if (mounted) state = AsyncValue.data(merged);
        }
      },
      onError: (e) => _log('Timeline stream error for app ${_params.appId}: $e'),
    );

    _connSub = _repository.connectionStateStream.listen(
      (isConnected) {
        if (isConnected) {
          _requestRefresh();
        }
      },
      onError: (e) => _log('Connection stream error for app ${_params.appId}: $e'),
    );

    // Initial refresh
    _requestRefresh();
  }

  void _requestRefresh() {
    _log('Requesting timeline refresh for app ${_params.appId}');
    _repository.getTimeline(
      startDate: _params.startDate,
      endDate: _params.endDate,
      appId: _params.appId,
    ).catchError((e) => _log('Timeline request failed for app ${_params.appId}: $e'));
  }

  void _updateRepository(WebSocketTimeTrackerRepository newRepo) {
    _timelineSub?.cancel();
    _connSub?.cancel();
    _repository = newRepo;
    _setupStreams();
  }

  Future<void> refresh() async {
    _requestRefresh();
  }

  @override
  void dispose() {
    _timelineSub?.cancel();
    _connSub?.cancel();
    super.dispose();
  }
}

final allTimeTimelineProvider = AsyncNotifierProvider<AllTimeTimelineNotifier, List<TimelineModel>>(
  AllTimeTimelineNotifier.new,
);

class AllTimeTimelineNotifier extends AsyncNotifier<List<TimelineModel>> {
  late WebSocketTimeTrackerRepository _repository;
  bool _requestInFlight = false;
  StreamSubscription<List<TimelineModel>>? _timelineSub;
  StreamSubscription<bool>? _connSub;

  @override
  Future<List<TimelineModel>> build() async {
    _repository = _asWebSocket(ref.watch(timeTrackerRepositoryProvider))!;

    // Set up persistence for timeline data
    await persist(
      ref.watch(storageProvider.future),
      key: 'all_time_timeline',
      options: const StorageOptions(cacheTime: StorageCacheTime(Duration(hours: 6))),
      encode: (timeline) {
        print('AllTimeTimelineNotifier: Encoding ${timeline.length} timeline entries to cache');
        if (timeline.isEmpty) {
          print('AllTimeTimelineNotifier: WARNING - Encoding empty timeline data! This will overwrite cached data.');
        }
        return jsonEncode(timeline.map((entry) => entry.toJson()).toList());
      },
      decode: (json) {
        final decoded = jsonDecode(json) as List;
        final timeline = decoded
            .map((e) => TimelineModel.fromJson(e as Map<String, dynamic>))
            .toList();
        print('AllTimeTimelineNotifier: Decoding ${timeline.length} timeline entries from cache');
        return timeline;
      },
    ).future;

    // Wait for cached data to be loaded (possible without?)
    int count = 0;
    while (state.value == null || count < 10) {
      await Future.delayed(const Duration(milliseconds: 100));
      count++;
    }

    ref.listen<TimeTrackerRepository>(timeTrackerRepositoryProvider, (prev, next) {
      if (prev != next) {
        updateRepository(next);
      }
    });

    _setupAndRefresh();

    return state.value ?? <TimelineModel>[];
  }

  void updateRepository(TimeTrackerRepository newRepository) {
    final ws = _asWebSocket(newRepository);
    if (ws == null) return;
    if (identical(ws, _repository)) return;

    try {
      _timelineSub?.cancel();
    } catch (_) {}
    _timelineSub = null;
    try {
      _connSub?.cancel();
    } catch (_) {}
    _connSub = null;

    _repository = ws;
    _requestInFlight = false;

    // Set up streams in background
    _setupStreams();
  }

  Future<void> _setupAndRefresh() async {
    // Run setup and refresh in background without blocking the build method
    try {
      if (_kDebug) {
        _log('AllTimeTimelineNotifier: Starting setup and background refresh...');
      }
      await _setupStreams();
      await _backgroundRefresh();
    } catch (e) {
      if (_kDebug) {
        _log('AllTimeTimelineNotifier: Setup and refresh failed: $e');
      }
      // Failure is fine - we already returned cached data
    }
  }

  Future<void> _setupStreams() async {
    _timelineSub = _repository.timelineStream.listen(
      (timeline) {
        if (_kDebug) {
          _log('AllTimeTimelineNotifier: received timeline items=${timeline.length}');
        }
        // Only update state if we have actual timeline data
        // This prevents overwriting cached data with empty lists when offline
        if (timeline.isNotEmpty) {
          final existingData = state.when(
            data: (data) => data,
            loading: () => <TimelineModel>[],
            error: (_, __) => <TimelineModel>[],
          );
          final mergedTimeline = _mergeTimelineEntries(existingData, timeline);
          print('AllTimeTimelineNotifier: Updating state with ${mergedTimeline.length} timeline entries');
          state = AsyncData(mergedTimeline);
        } else {
          print('AllTimeTimelineNotifier: Received empty timeline from stream, keeping current state');
        }
      },
      onError: (e, st) {
        if (!state.hasValue) {
          state = const AsyncLoading();
        }
        _log('AllTimeTimelineNotifier.timelineStream error: $e');
      },
      onDone: () {
        if (_kDebug) {
          _log('AllTimeTimelineNotifier.timelineStream done');
        }
      },
      cancelOnError: false,
    );

    _connSub = _repository.connectionStateStream.listen(
      (isConnected) {
        if (_kDebug) {
          _log('AllTimeTimelineNotifier: connectionState=$isConnected');
        }
        if (isConnected) {
          _requestTimelineIfIdle();
        }
      },
      onError: (err) {
        if (_kDebug) {
          _log('AllTimeTimelineNotifier: connectionStateStream error: $err');
        }
      },
      onDone: () {
        if (_kDebug) {
          _log('AllTimeTimelineNotifier.connectionStateStream done');
        }
      },
      cancelOnError: false,
    );

    // Connection and timeline requests are now handled by _backgroundRefresh()
    // This method only sets up stream listeners
  }

  void _requestTimelineIfIdle() {
    if (_requestInFlight) return;
    _requestInFlight = true;

    if (_kDebug) {
      _log('AllTimeTimelineNotifier: requesting timeline...');
    }

    _repository.getTimeline().then((_) {
      if (_kDebug) {
        _log('AllTimeTimelineNotifier: getTimeline() request sent');
      }
    }).catchError((e, _) {
      _log('AllTimeTimelineNotifier.getTimeline failed: $e');
    }).whenComplete(() {
      _requestInFlight = false;
    });
  }

  Future<void> _backgroundRefresh() async {
    // Request timeline data without manual connection (repository handles it)
    try {
      if (_kDebug) {
        _log('AllTimeTimelineNotifier: Starting background refresh...');
      }
      _requestTimelineIfIdle();
    } catch (e) {
      if (_kDebug) {
        _log('AllTimeTimelineNotifier: Background refresh failed: $e');
      }
      // Background failure is fine - we already returned cached data
    }
  }

  Future<void> refresh() async {
    // Repository handles connection internally
    _requestTimelineIfIdle();
  }

}