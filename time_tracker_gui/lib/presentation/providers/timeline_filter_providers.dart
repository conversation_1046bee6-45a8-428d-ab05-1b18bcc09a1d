import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/models/app_model.dart';
import '../widgets/timeline/timeline_filter_state.dart';
import '../widgets/timeline/timeline_enums.dart';
import '../widgets/timeline/timeline_filter_notifier.dart';
import '../widgets/timeline/timeline_shared_utils.dart';
import 'app_timeline_providers.dart';
import 'state_providers.dart';

/// Optimized provider for app lookup map to avoid O(n²) complexity
final appLookupMapProvider = Provider<AsyncValue<Map<int, AppModel>>>((ref) {
  final apps = ref.watch(appsProvider);
  return apps.when(
    data: (appList) {
      final Map<int, AppModel> appMap = {};
      for (final app in appList) {
        appMap[app.id] = app;
      }
      return AsyncValue.data(appMap);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

/// Optimized provider for filtered and sorted timeline data
final filteredTimelineProvider = Provider<AsyncValue<List<TimelineModel>>>((ref) {
  final timeline = ref.watch(allTimeTimelineProvider);
  final appMap = ref.watch(appLookupMapProvider);
  final filterState = ref.watch(timelineFilterProvider);

  return timeline.when(
    data: (timelineData) => appMap.when(
      data: (appLookupMap) {
        final filteredData = _filterAndSortTimelineOptimized(timelineData, appLookupMap, filterState);
        return AsyncValue.data(filteredData);
      },
      loading: () => const AsyncValue.loading(),
      error: (error, stack) => AsyncValue.error(error, stack),
    ),
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

/// Optimized filtering and sorting function with O(n) complexity
List<TimelineModel> _filterAndSortTimelineOptimized(
  List<TimelineModel> timeline,
  Map<int, AppModel> appMap,
  TimelineFilterState filterState,
) {
  return filterAndSortTimelineOptimized(timeline, appMap, filterState);
}
