import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_riverpod/legacy.dart';
import '../../data/repositories/time_tracker_repository.dart';
import 'service_providers.dart';

const bool _kProvidersDebug = true;
void _d(String msg) {
  if (_kProvidersDebug) {
    // ignore: avoid_print
    print('[connection_status_providers] $msg');
  }
}

WebSocketTimeTrackerRepository? _asWs(TimeTrackerRepository r) =>
    r is WebSocketTimeTrackerRepository ? r : null;

// ---------- Connection Status ----------

enum ConnectionType { websocket, http, disconnected }

class ConnectionStatus {
  final bool isConnected;
  final ConnectionType connectionType;
  final String? errorMessage;

  const ConnectionStatus({
    required this.isConnected,
    required this.connectionType,
    this.errorMessage,
  });

  ConnectionStatus copyWith({
    bool? isConnected,
    ConnectionType? connectionType,
    String? errorMessage,
 }) {
    return ConnectionStatus(
      isConnected: isConnected ?? this.isConnected,
      connectionType: connectionType ?? this.connectionType,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

final connectionStatusProvider =
    StateNotifierProvider<ConnectionStatusNotifier, ConnectionStatus>((ref) {
  final repository = ref.watch(timeTrackerRepositoryProvider);
  final notifier = ConnectionStatusNotifier(repository);

  ref.listen(timeTrackerRepositoryProvider, (previous, next) {
    if (previous != next) {
      notifier.updateRepository(next);
    }
  });

  return notifier;
});

class ConnectionStatusNotifier extends StateNotifier<ConnectionStatus> {
  TimeTrackerRepository _repository;
  Timer? _statusCheckTimer;
  StreamSubscription<bool>? _connectionStateSubscription;

  ConnectionStatusNotifier(this._repository)
      : super(const ConnectionStatus(
          isConnected: false,
          connectionType: ConnectionType.disconnected,
        )) {
    _initializeConnection();
    _setupConnectionStateListener();
 }

  Future<void> _initializeConnection() async {
    if (!mounted) return;

    try {
      await _repository.connect();
      if (mounted) _updateConnectionStatus();
    } catch (e) {
      if (mounted) {
        state = ConnectionStatus(
          isConnected: false,
          connectionType: ConnectionType.disconnected,
          errorMessage: e.toString(),
        );
      }
    }
  }

  void _setupConnectionStateListener() {
    final ws = _asWs(_repository);
    if (ws != null) {
      _connectionStateSubscription = ws.connectionStateStream.listen(
        (isConnected) {
          if (mounted) {
            state = ConnectionStatus(
              isConnected: isConnected,
              connectionType:
                  isConnected ? ConnectionType.websocket : ConnectionType.disconnected,
            );
          }
        },
        onError: (error) {
          if (mounted) {
            state = ConnectionStatus(
              isConnected: false,
              connectionType: ConnectionType.disconnected,
              errorMessage: error.toString(),
            );
          }
        },
      );

      _statusCheckTimer =
          Timer.periodic(const Duration(seconds: 5), (_) => _updateConnectionStatus());

      _updateConnectionStatus();
    } else {
      _startStatusMonitoring();
    }
  }

  void _startStatusMonitoring() {
    _statusCheckTimer =
        Timer.periodic(const Duration(seconds: 5), (_) => _updateConnectionStatus());
  }

  void _updateConnectionStatus() {
    if (!mounted) return;

    final ws = _asWs(_repository);
    if (ws != null) {
      final isConnected = ws.isConnected;
      state = ConnectionStatus(
        isConnected: isConnected,
        connectionType: isConnected ? ConnectionType.websocket : ConnectionType.disconnected,
      );
    } else {
      state = const ConnectionStatus(
        isConnected: true,
        connectionType: ConnectionType.http,
      );
    }
  }

  @override
  void dispose() {
    _statusCheckTimer?.cancel();
    _connectionStateSubscription?.cancel();
    super.dispose();
  }

  void updateRepository(TimeTrackerRepository newRepository) {
    if (!mounted) return;

    _statusCheckTimer?.cancel();
    _connectionStateSubscription?.cancel();

    _repository = newRepository;

    if (mounted) {
      _initializeConnection();
      _setupConnectionStateListener();
    }
  }
}