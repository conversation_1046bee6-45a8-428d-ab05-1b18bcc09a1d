import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/services/poster_cache_service.dart';
import '../../data/models/poster_model.dart';
import '../../data/models/app_model.dart';
import 'state_providers.dart';

final posterCacheServiceProvider = Provider<PosterCacheService>((ref) {
  return PosterCacheService();
});

final cachedPosterProvider = FutureProvider.family<Poster?, String>((ref, gameName) async {
  final cacheService = ref.read(posterCacheServiceProvider);
  return await cacheService.searchAndCachePoster(gameName);
});

final cachedImagePathProvider = FutureProvider.family<String?, String>((ref, gameName) async {
  final cacheService = ref.read(posterCacheServiceProvider);
  return await cacheService.getCachedImagePath(gameName);
});

final cacheStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final cacheService = ref.read(posterCacheServiceProvider);
  final cacheSize = await cacheService.getCacheSize();

  return {
    'size': cacheSize,
    'sizeFormatted': _formatBytes(cacheSize),
  };
});

final initializePosterCacheProvider = FutureProvider<void>((ref) async {
  final appsAsyncValue = ref.watch(appsProvider);

  await appsAsyncValue.when(
    data: (apps) async {
      if (apps.isEmpty) {
        return;
      }

      final cacheService = ref.read(posterCacheServiceProvider);

      final validApps = apps.where((app) {
        final gameName = _getGameName(app);
        return gameName.isNotEmpty;
      }).toList();

      if (validApps.isEmpty) {
        return;
      }

      const batchSize = 3;
      int cachedCount = 0;

      for (int i = 0; i < validApps.length; i += batchSize) {
        final batch = validApps.skip(i).take(batchSize);

        final futures = batch.map((app) async {
          try {
            final gameName = _getGameName(app);

            final existingPoster = await cacheService.getCachedPoster(gameName);
            if (existingPoster != null) {
              return true;
            }

            final isFirstBatch = i == 0;
            final poster = isFirstBatch
                ? await cacheService.searchAndCachePosterFast(gameName)
                : await cacheService.searchAndCachePoster(gameName);

            return poster != null;
          } catch (e) {
            return false;
          }
        });

        final results = await Future.wait(futures);
        cachedCount += results.where((success) => success).length;

        if (i + batchSize < validApps.length) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }
    },
    loading: () {},
    error: (error, stackTrace) {},
  );
});

final clearCacheProvider = FutureProvider<void>((ref) async {
  final cacheService = ref.read(posterCacheServiceProvider);
  await cacheService.clearAllCache();

  ref.invalidate(cacheStatsProvider);
});

String _formatBytes(int bytes) {
  if (bytes < 1024) return '$bytes B';
  if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
  if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
}

final localGamePostersProvider = FutureProvider.family<Map<int, Poster?>, List<AppModel>>((ref, apps) async {
  final cacheService = ref.read(posterCacheServiceProvider);
  final Map<int, Poster?> posters = {};

  const batchSize = 5;
  for (int i = 0; i < apps.length; i += batchSize) {
    final batch = apps.skip(i).take(batchSize);

    final futures = batch.map((app) async {
      final gameName = _getGameName(app);
      if (gameName.isNotEmpty) {
        final poster = await cacheService.searchAndCachePoster(gameName);
        return MapEntry(app.id, poster);
      }
      return MapEntry(app.id, null);
    });

    final results = await Future.wait(futures);
    for (final result in results) {
      posters[result.key] = result.value;
    }

    if (i + batchSize < apps.length) {
      await Future.delayed(const Duration(milliseconds: 500));
    }
  }

  return posters;
});

String _getGameName(AppModel app) {
  String gameName = '';

  if (app.productName?.isNotEmpty == true) {
    gameName = app.productName!;
  } else if (app.name?.isNotEmpty == true) {
    gameName = app.name!;
  }

  gameName = gameName
      .replaceAll(RegExp(r'\.exe$', caseSensitive: false), '')
      .replaceAll(RegExp(r'\s+'), ' ')
      .trim();

  return gameName;
}