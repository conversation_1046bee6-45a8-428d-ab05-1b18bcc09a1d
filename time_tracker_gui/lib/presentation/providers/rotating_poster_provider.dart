import 'dart:async';
import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_riverpod/legacy.dart';
import '../../data/models/app_model.dart';
import '../../data/models/poster_model.dart';
import '../../data/services/poster_cache_service.dart';
import 'state_providers.dart';
import 'local_game_poster_providers.dart';

class RotatingPosterState {
  final Poster? currentPoster;
  final AppModel? currentApp;
  final bool isLoading;

  const RotatingPosterState({
    this.currentPoster,
    this.currentApp,
    this.isLoading = false,
  });

  RotatingPosterState copyWith({
    Poster? currentPoster,
    AppModel? currentApp,
    bool? isLoading,
  }) {
    return RotatingPosterState(
      currentPoster: currentPoster ?? this.currentPoster,
      currentApp: currentApp ?? this.currentApp,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

class RotatingPosterNotifier extends StateNotifier<RotatingPosterState> {
  final Ref _ref;
  Timer? _rotationTimer;
  final Random _random = Random();

  RotatingPosterNotifier(this._ref) : super(const RotatingPosterState(isLoading: true)) {
    _loadImmediatePoster();
    _startRotation();

    _ref.listen(appsProvider, (previous, next) {
      if (next.hasValue && next.value!.isNotEmpty && state.currentPoster == null) {
        _loadImmediatePoster();
      }
    });
  }

  @override
  void dispose() {
    _rotationTimer?.cancel();
    super.dispose();
  }

  void _loadImmediatePoster() async {
    try {
      final appsAsyncValue = _ref.read(appsProvider);

      if (appsAsyncValue.hasValue && appsAsyncValue.value!.isNotEmpty) {
        final apps = appsAsyncValue.value!;
        final validApps = apps.where((app) =>
          (app.productName?.isNotEmpty ?? false) || (app.name?.isNotEmpty ?? false)
        ).toList();

        if (validApps.isNotEmpty) {
          final cacheService = _ref.read(posterCacheServiceProvider);

          final List<AppModel> appsWithCachedPosters = [];
          for (final app in validApps) {
            final gameName = _getGameName(app);
            final cachedPoster = await cacheService.getCachedPoster(gameName);
            if (cachedPoster != null && cachedPoster.hasImage) {
              appsWithCachedPosters.add(app);
            }
          }

          if (appsWithCachedPosters.isNotEmpty) {
            final randomApp = appsWithCachedPosters[_random.nextInt(appsWithCachedPosters.length)];
            final gameName = _getGameName(randomApp);
            final cachedPoster = await cacheService.getCachedPoster(gameName);

            if (cachedPoster != null && cachedPoster.hasImage) {
              state = RotatingPosterState(
                currentPoster: cachedPoster,
                currentApp: randomApp,
                isLoading: false,
              );
              return;
            }
          }
        }
      }
    } catch (e) {
      // Handle error silently
    }
  }

  void _startRotation() {
    _loadRandomPoster();
    _rotationTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _loadRandomPoster();
    });
  }

  Future<void> _loadRandomPoster() async {
    try {
      state = state.copyWith(isLoading: true);

      final appsAsyncValue = _ref.read(appsProvider);

      await appsAsyncValue.when(
        data: (apps) async {
          if (apps.isEmpty) {
            state = const RotatingPosterState(isLoading: false);
            return;
          }

          final validApps = apps.where((app) {
            final gameName = _getGameName(app);
            return gameName.isNotEmpty;
          }).toList();

          if (validApps.isEmpty) {
            state = const RotatingPosterState(isLoading: false);
            return;
          }

          final cacheService = _ref.read(posterCacheServiceProvider);

          final shuffledValidApps = List<AppModel>.from(validApps);
          shuffledValidApps.shuffle(_random);

          final List<AppModel> appsWithPosters = [];
          for (final app in shuffledValidApps) {
            final gameName = _getGameName(app);
            final cachedPoster = await cacheService.getCachedPoster(gameName);
            if (cachedPoster != null && cachedPoster.hasImage) {
              appsWithPosters.add(app);
            }
          }

          if (appsWithPosters.isEmpty) {
            final firstPoster = await _fetchFirstPosterFast(validApps, cacheService);
            if (firstPoster != null) {
              for (final app in validApps) {
                final gameName = _getGameName(app);
                final cachedPoster = await cacheService.getCachedPoster(gameName);
                if (cachedPoster != null && cachedPoster.hasImage) {
                  appsWithPosters.add(app);
                  break;
                }
              }
            }
          }

          if (appsWithPosters.isEmpty) {
            state = const RotatingPosterState(isLoading: false);
            return;
          }

          final randomApp = appsWithPosters[_random.nextInt(appsWithPosters.length)];
          final gameName = _getGameName(randomApp);

          final poster = await cacheService.getCachedPoster(gameName);

          state = RotatingPosterState(
            currentPoster: poster,
            currentApp: randomApp,
            isLoading: false,
          );
        },
        loading: () {
          state = const RotatingPosterState(isLoading: true);
        },
        error: (error, stackTrace) {
          state = const RotatingPosterState(isLoading: false);
        },
      );
    } catch (e) {
      state = const RotatingPosterState(isLoading: false);
    }
  }

  Future<Poster?> _fetchFirstPosterFast(List<AppModel> apps, PosterCacheService cacheService) async {
    final futures = apps.take(3).map((app) async {
      try {
        final gameName = _getGameName(app);
        final poster = await cacheService.searchAndCachePosterFast(gameName);
        return poster;
      } catch (e) {
        return null;
      }
    });

    final results = await Future.wait(futures);
    return results.firstWhere((poster) => poster != null, orElse: () => null);
  }

  String _getGameName(AppModel app) {
    String gameName = '';

    if (app.productName?.isNotEmpty == true) {
      gameName = app.productName!;
    } else if (app.name?.isNotEmpty == true) {
      gameName = app.name!;
    }

    gameName = gameName
        .replaceAll(RegExp(r'\.exe$', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();

    return gameName;
  }

  void forceRotate() {
    _loadRandomPoster();
  }
}

final rotatingPosterProvider = StateNotifierProvider<RotatingPosterNotifier, RotatingPosterState>((ref) {
  return RotatingPosterNotifier(ref);
});