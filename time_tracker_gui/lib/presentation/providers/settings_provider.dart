import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_riverpod/legacy.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class AppSettings {
  final String backendUrl;
  final bool enableNotifications;
  final bool enableAutoStart;
  final int refreshInterval;
  final String theme;

  const AppSettings({
    this.backendUrl = 'http://localhost:6754',
    this.enableNotifications = true,
    this.enableAutoStart = false,
    this.refreshInterval = 5,
    this.theme = 'system',
  });

  AppSettings copyWith({
    String? backendUrl,
    bool? enableNotifications,
    bool? enableAutoStart,
    int? refreshInterval,
    String? theme,
  }) {
    return AppSettings(
      backendUrl: backendUrl ?? this.backendUrl,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableAutoStart: enableAutoStart ?? this.enableAutoStart,
      refreshInterval: refreshInterval ?? this.refreshInterval,
      theme: theme ?? this.theme,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'backendUrl': backendUrl,
      'enableNotifications': enableNotifications,
      'enableAutoStart': enableAutoStart,
      'refreshInterval': refreshInterval,
      'theme': theme,
    };
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      backendUrl: json['backendUrl'] ?? 'http://localhost:6754',
      enableNotifications: json['enableNotifications'] ?? true,
      enableAutoStart: json['enableAutoStart'] ?? false,
      refreshInterval: json['refreshInterval'] ?? 5,
      theme: json['theme'] ?? 'system',
    );
  }
}

final settingsProvider = NotifierProvider<SettingsNotifier, AppSettings>(
  SettingsNotifier.new,
);

class SettingsNotifier extends Notifier<AppSettings> {
  static const String _settingsKey = 'app_settings';

  @override
  AppSettings build() {
    _loadSettings();
    return const AppSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);

      if (settingsJson != null) {
        final Map<String, dynamic> json = jsonDecode(settingsJson);
        state = AppSettings.fromJson(json);
      }
    } catch (e) {
      // If loading fails, keep default settings
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(state.toJson());
      await prefs.setString(_settingsKey, jsonString);
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> updateBackendUrl(String url) async {
    state = state.copyWith(backendUrl: url);
    await _saveSettings();
  }

  Future<void> updateNotifications(bool enabled) async {
    state = state.copyWith(enableNotifications: enabled);
    await _saveSettings();
  }

  Future<void> updateAutoStart(bool enabled) async {
    state = state.copyWith(enableAutoStart: enabled);
    await _saveSettings();
  }

  Future<void> updateRefreshInterval(int interval) async {
    state = state.copyWith(refreshInterval: interval);
    await _saveSettings();
  }

  Future<void> updateTheme(String theme) async {
    state = state.copyWith(theme: theme);
    await _saveSettings();
  }

  Future<void> resetToDefaults() async {
    state = const AppSettings();
    await _saveSettings();
  }

  bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }
}
