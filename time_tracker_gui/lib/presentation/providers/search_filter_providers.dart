import 'dart:async';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_riverpod/legacy.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../data/models/app_model.dart';
import 'state_providers.dart';
import 'app_timeline_providers.dart';

const bool _kProvidersDebug = true;
void _d(String msg) {
  if (_kProvidersDebug) {
    // ignore: avoid_print
    print('[search_filter_providers] $msg');
  }
}

int _compareDateDesc(DateTime? a, DateTime? b) {
  if (a == null && b == null) return 0;
  if (a == null) return 1;
  if (b == null) return -1;
  return b.compareTo(a);
}

// ---------- Search and Filter State ----------

enum AppSortBy { name, duration, launches, lastUsed }
enum SortOrder { ascending, descending }

class AppSearchState {
  final String searchQuery;
  final AppSortBy sortBy;
  final SortOrder sortOrder;

  const AppSearchState({
    this.searchQuery = '',
    this.sortBy = AppSortBy.name,
    this.sortOrder = SortOrder.ascending,
  });

  AppSearchState copyWith({
    String? searchQuery,
    AppSortBy? sortBy,
    SortOrder? sortOrder,
 }) {
    return AppSearchState(
      searchQuery: searchQuery ?? this.searchQuery,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'searchQuery': searchQuery,
      'sortBy': sortBy.index,
      'sortOrder': sortOrder.index,
    };
  }

  factory AppSearchState.fromJson(Map<String, dynamic> json) {
    return AppSearchState(
      searchQuery: json['searchQuery'] ?? '',
      sortBy: AppSortBy.values[json['sortBy'] ?? 0],
      sortOrder: SortOrder.values[json['sortOrder'] ?? 0],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppSearchState &&
        other.searchQuery == searchQuery &&
        other.sortBy == sortBy &&
        other.sortOrder == sortOrder;
 }

  @override
  int get hashCode => Object.hash(searchQuery, sortBy, sortOrder);
}

final appSearchProvider =
    StateNotifierProvider<AppSearchNotifier, AppSearchState>((ref) {
  return AppSearchNotifier();
});

class AppSearchNotifier extends StateNotifier<AppSearchState> {
  static const String _appSearchKey = 'app_search_state';
  Timer? _saveTimer;

  AppSearchNotifier() : super(const AppSearchState()) {
    _loadSearchState();
  }

  @override
  void dispose() {
    _saveTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadSearchState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final searchJson = prefs.getString(_appSearchKey);
      if (searchJson != null) {
        final Map<String, dynamic> json = jsonDecode(searchJson);
        state = AppSearchState.fromJson(json);
      }
    } catch (e) {
      _d('Failed to load app search state: $e');
    }
  }

  Future<void> _saveSearchState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(state.toJson());
      await prefs.setString(_appSearchKey, jsonString);
    } catch (e) {
      _d('Failed to save app search state: $e');
    }
  }

  void _debouncedSave() {
    _saveTimer?.cancel();
    _saveTimer = Timer(const Duration(milliseconds: 500), _saveSearchState);
  }

  void updateSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
    _debouncedSave();
  }

  void updateSortBy(AppSortBy sortBy) {
    state = state.copyWith(sortBy: sortBy);
    _debouncedSave();
  }

  void updateSortOrder(SortOrder sortOrder) {
    state = state.copyWith(sortOrder: sortOrder);
    _debouncedSave();
  }

  void toggleSortOrder() {
    final newOrder =
        state.sortOrder == SortOrder.ascending ? SortOrder.descending : SortOrder.ascending;
    state = state.copyWith(sortOrder: newOrder);
    _debouncedSave();
  }

  void clearSearch() {
    state = state.copyWith(searchQuery: '');
    _debouncedSave();
  }

  void clearAllFilters() {
    _saveTimer?.cancel();
    state = const AppSearchState();
    _saveSearchState();
  }
}

// ---------- Enhanced types ----------

class AppWithLastUsed {
  final AppModel app;
  final DateTime? lastUsedDate;

  const AppWithLastUsed({
    required this.app,
    this.lastUsedDate,
  });
}

final appsWithLastUsedCacheProvider =
    Provider<AsyncValue<List<AppWithLastUsed>>>((ref) {
  final appsAsync = ref.watch(appsProvider);
  final timelineAsync = ref.watch(allTimeTimelineProvider);

  if (_kProvidersDebug) {
    final String appsState = appsAsync.when(
      data: (apps) => 'data(${apps.length})',
      loading: () => 'loading',
      error: (e, _) => 'error:$e',
    );
    final String timelineState = timelineAsync.when(
      data: (timeline) => 'data(${timeline.length})',
      loading: () => 'loading',
      error: (e, _) => 'error:$e',
    );
    _d('appsWithLastUsedCacheProvider: apps=$appsState timeline=$timelineState');
    
    // Additional debug info
    _d('appsProvider state details: isLoading=${appsAsync.isLoading}, hasError=${appsAsync.hasError}, hasValue=${appsAsync.hasValue}');
  }

  // Use cached data if available, even while loading fresh data
  if (appsAsync.isLoading && !appsAsync.hasValue) {
    return const AsyncValue.loading();
  }
  if (appsAsync.hasError && !appsAsync.hasValue) {
    final Object error = appsAsync.error ?? Exception('Unknown apps error');
    final StackTrace st = appsAsync.stackTrace ?? StackTrace.empty;
    return AsyncValue.error(error, st);
  }

  final List<AppModel> apps = appsAsync.hasValue ? appsAsync.requireValue : [];

  // Use cached timeline data if available, even while loading fresh data
  if (timelineAsync.isLoading && !timelineAsync.hasValue) {
    if (_kProvidersDebug) {
      _d('appsWithLastUsedCacheProvider: timeline loading with no cached data, waiting for data');
    }
    return const AsyncValue.loading();
  }
  if (timelineAsync.hasError && !timelineAsync.hasValue) {
    final Object err = timelineAsync.error ?? Exception('Unknown timeline error');
    final StackTrace st = timelineAsync.stackTrace ?? StackTrace.empty;
    if (_kProvidersDebug) {
      _d('appsWithLastUsedCacheProvider: timeline error with no cached data ($err), surfacing error');
    }
    return AsyncValue.error(err, st);
  }

  final List<TimelineModel> timeline = timelineAsync.hasValue ? timelineAsync.requireValue : [];

  final Map<int, DateTime?> latestByAppId = <int, DateTime?>{};
  for (final TimelineModel t in timeline) {
    final int? id = t.appId;
    if (id == null) {
      continue;
    }
    final DateTime? d = t.date;
    final DateTime? prev = latestByAppId[id];
    if (prev == null) {
      latestByAppId[id] = d;
    } else if (d != null && d.isAfter(prev)) {
      latestByAppId[id] = d;
    }
  }

  final List<AppWithLastUsed> result = <AppWithLastUsed>[
    for (final AppModel a in apps)
      AppWithLastUsed(
        app: a,
        lastUsedDate: a.id != null ? latestByAppId[a.id!] : null,
      ),
  ];

  if (_kProvidersDebug) {
    _d('appsWithLastUsedCacheProvider: produced ${result.length} items with timeline data (waited until ready)');
  }

  return AsyncValue.data(result);
});

final recentAppsProvider = Provider<AsyncValue<List<AppModel>>>((ref) {
  final appsWithLastUsed = ref.watch(appsWithLastUsedCacheProvider);

  return appsWithLastUsed.when(
    data: (items) {
      final sorted = List<AppWithLastUsed>.from(items)
        ..sort((a, b) => _compareDateDesc(a.lastUsedDate, b.lastUsedDate));
      final recentApps = sorted.take(5).map((e) => e.app).toList();
      return AsyncValue.data(recentApps);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) {
      _d('recentAppsProvider error: $error');
      return AsyncValue.error(error, stackTrace);
    },
  );
});

final filteredAppsProvider = Provider<AsyncValue<List<AppModel>>>((ref) {
  final appsWithLastUsed = ref.watch(appsWithLastUsedCacheProvider);
  final searchState = ref.watch(appSearchProvider);

  return appsWithLastUsed.when(
    data: (items) {
      Iterable<AppWithLastUsed> filtered = items;

      if (searchState.searchQuery.isNotEmpty) {
        final q = searchState.searchQuery.toLowerCase();
        filtered = filtered.where((e) {
          final app = e.app;
          return (app.name?.toLowerCase().contains(q) ?? false) ||
              (app.productName?.toLowerCase().contains(q) ?? false);
        });
      }

      final list = filtered.toList();
      switch (searchState.sortBy) {
        case AppSortBy.name:
          list.sort((a, b) => (a.app.name ?? '').compareTo(b.app.name ?? ''));
          break;
        case AppSortBy.duration:
          list.sort((a, b) =>
              (b.app.duration ?? 0).compareTo(a.app.duration ?? 0));
          break;
        case AppSortBy.launches:
          list.sort((a, b) =>
              (b.app.launches ?? 0).compareTo(a.app.launches ?? 0));
          break;
        case AppSortBy.lastUsed:
          list.sort(
              (a, b) => _compareDateDesc(a.lastUsedDate, b.lastUsedDate));
          break;
      }

      return AsyncValue.data(list.map((e) => e.app).toList());
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});