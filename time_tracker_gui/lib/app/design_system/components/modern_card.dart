import 'package:flutter/material.dart';
import '../design_tokens.dart';
import '../app_colors.dart';
import 'base_card.dart';

/// Modern card components for the Time Tracker app
/// Provides consistent styling and behavior across all cards

class ModernStatsCard extends StatelessWidget {
  const ModernStatsCard({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    this.icon,
    this.accentColor,
    this.onTap,
    this.isLoading = false,
  });

  final String title;
  final String value;
  final String? subtitle;
  final IconData? icon;
  final Color? accentColor;
  final VoidCallback? onTap;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final accent = accentColor ?? theme.colorScheme.primary;
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    return BaseCard(
      onTap: onTap,
      variant: CardVariant.elevated,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Container(
                  padding: EdgeInsets.all(isMobile ? DesignTokens.spacingXS : DesignTokens.spacingS),
                  decoration: BoxDecoration(
                    color: accent.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(DesignTokens.radiusS),
                  ),
                  child: Icon(
                    icon,
                    color: accent,
                    size: isMobile ? DesignTokens.iconXS : DesignTokens.iconS,
                  ),
                ),
                SizedBox(width: isMobile ? DesignTokens.spacingS : DesignTokens.spacingM),
              ],
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontSize: isMobile ? 12 : null,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: isMobile ? DesignTokens.spacingS : DesignTokens.spacingM),
          if (isLoading)
            const CircularProgressIndicator()
          else
            Text(
              value,
              style: theme.textTheme.headlineMedium?.copyWith(
                color: accent,
                fontWeight: FontWeight.w700,
                fontSize: isMobile ? 20 : null, // Smaller value text on mobile
              ),
            ),
          if (subtitle != null) ...[
            SizedBox(height: isMobile ? 2 : DesignTokens.spacingXS),
            Text(
              subtitle!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontSize: isMobile ? 10 : null,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class ModernInfoCard extends StatelessWidget {
  const ModernInfoCard({
    super.key,
    required this.title,
    required this.child,
    this.icon,
    this.actions,
    this.onTap,
  });

  final String title;
  final Widget child;
  final IconData? icon;
  final List<Widget>? actions;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    return BaseCard(
      onTap: onTap,
      variant: CardVariant.elevated,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  color: theme.colorScheme.primary,
                  size: isMobile ? DesignTokens.iconS : DesignTokens.iconM,
                ),
                SizedBox(width: isMobile ? DesignTokens.spacingS : DesignTokens.spacingM),
              ],
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: isMobile ? 14 : null, // Smaller title on mobile
                  ),
                ),
              ),
              if (actions != null) ...actions!,
            ],
          ),
          SizedBox(height: isMobile ? DesignTokens.spacingS : DesignTokens.spacingM),
          child,
        ],
      ),
    );
  }
}

class ModernHeroCard extends StatelessWidget {
  const ModernHeroCard({
    super.key,
    required this.child,
    this.backgroundImage,
    this.gradient,
    this.onTap,
  });

  final Widget child;
  final ImageProvider? backgroundImage;
  final Gradient? gradient;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.all(DesignTokens.spacingM),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(DesignTokens.radiusL),
          child: Container(
            padding: const EdgeInsets.all(DesignTokens.spacingXL),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(DesignTokens.radiusL),
              image: backgroundImage != null
                  ? DecorationImage(
                      image: backgroundImage!,
                      fit: BoxFit.cover,
                    )
                  : null,
              gradient: gradient ?? 
                  (isDark ? AppColors.darkGradient : AppColors.primaryGradient),
            ),
            child: child,
          ),
        ),
      ),
    );
  }
}

class ModernActionCard extends StatelessWidget {
  const ModernActionCard({
    super.key,
    required this.title,
    required this.onTap,
    this.subtitle,
    this.icon,
    this.trailing,
  });

  final String title;
  final String? subtitle;
  final IconData? icon;
  final Widget? trailing;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BaseCard(
      onTap: onTap,
      variant: CardVariant.outlined,
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: theme.colorScheme.primary,
              size: DesignTokens.iconM,
            ),
            const SizedBox(width: DesignTokens.spacingM),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: DesignTokens.spacingXS),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) ...[
            const SizedBox(width: DesignTokens.spacingM),
            trailing!,
          ] else ...[
            const SizedBox(width: DesignTokens.spacingM),
            Icon(
              Icons.arrow_forward_ios,
              color: theme.colorScheme.onSurfaceVariant,
              size: DesignTokens.iconS,
            ),
          ],
        ],
      ),
    );
  }
}

class ModernListCard extends StatelessWidget {
  const ModernListCard({
    super.key,
    required this.title,
    required this.items,
    this.icon,
    this.onViewAll,
    this.maxItems = 5,
  });

  final String title;
  final List<Widget> items;
  final IconData? icon;
  final VoidCallback? onViewAll;
  final int maxItems;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final displayItems = items.take(maxItems).toList();

    return BaseCard(
      variant: CardVariant.elevated,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  color: theme.colorScheme.primary,
                  size: DesignTokens.iconM,
                ),
                const SizedBox(width: DesignTokens.spacingM),
              ],
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (onViewAll != null)
                TextButton(
                  onPressed: onViewAll,
                  child: const Text('View All'),
                ),
            ],
          ),
          const SizedBox(height: DesignTokens.spacingM),
          ...displayItems.expand((item) => [
            item,
            if (item != displayItems.last)
              const SizedBox(height: DesignTokens.spacingS),
          ]),
        ],
      ),
    );
  }
}
