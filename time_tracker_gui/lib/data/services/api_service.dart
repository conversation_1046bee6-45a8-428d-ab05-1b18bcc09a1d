import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/app_model.dart';
import '../../core/constants/api_constants.dart';

class ApiService {
  final String baseUrl;
  final http.Client _client;

  ApiService({
    required this.baseUrl,
    http.Client? client,
  }) : _client = client ?? http.Client();

  Future<bool> testConnection() async {
    try {
      final response = await _client.get(
        Uri.parse('$baseUrl/api/apps'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5));
      
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  Future<List<AppModel>> getAllApps() async {
    final response = await _client.get(
      Uri.parse('$baseUrl/api/apps'),
      headers: {'Content-Type': 'application/json'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return (data['data'] as List)
            .map((app) => AppModel.fromJson(app))
            .toList();
      }
    }
    throw Exception('Failed to load apps');
  }

  Future<void> insertApp(String name) async {
    final response = await _client.post(
      Uri.parse('$baseUrl/api/apps'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({'name': name}),
    );

    if (response.statusCode != 201) {
      throw Exception('Failed to create app');
    }
  }

  Future<void> deleteApp(String processName) async {
    // Note: This API service may need backend changes to support delete by name
    // For now, keeping the API call structure similar but passing process name
    final response = await _client.delete(
      Uri.parse('$baseUrl/api/apps'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({'process_name': processName}),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to delete app');
    }
  }

  Future<TrackingStatus> getTrackingStatus() async {
    final response = await _client.get(
      Uri.parse('$baseUrl/api/tracking/status'),
      headers: {'Content-Type': 'application/json'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data['success'] == true && data['data'] != null) {
        return TrackingStatus.fromJson(data['data']);
      }
    }
    throw Exception('Failed to get tracking status');
  }

  Future<void> sendTrackingCommand(String command) async {
    final response = await _client.post(
      Uri.parse('$baseUrl/api/tracking/command'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({'command': command}),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to send tracking command');
    }
  }

  void dispose() {
    _client.close();
  }
} 