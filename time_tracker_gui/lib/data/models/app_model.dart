class AppModel {
  final int id;
  final String? name;
  final String? productName;
  final int? duration;
  final int? launches;
  final int? longestSession;
  final DateTime? longestSessionOn;

  const AppModel({
    required this.id,
    this.name,
    this.productName,
    this.duration,
    this.launches,
    this.longestSession,
    this.longestSessionOn,
  });

  factory AppModel.fromJson(Map<String, dynamic> json) {
    return AppModel(
      id: json['id'] as int,
      name: json['name'] as String?,
      productName: json['productName'] as String?,
      duration: json['duration'] as int?,
      launches: json['launches'] as int?,
      longestSession: json['longestSession'] as int?,
      longestSessionOn: json['longestSessionOn'] != null
          ? DateTime.parse(json['longestSessionOn'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'productName': productName,
      'duration': duration,
      'launches': launches,
      'longestSession': longestSession,
      'longestSessionOn': longestSessionOn?.toIso8601String(),
    };
  }
}

class TimelineModel {
  final int id;
  final DateTime? date;
  final int? duration;
  final int appId;
  final int? checkpointId;
  final List<int> checkpointAssociations;

  const TimelineModel({
    required this.id,
    this.date,
    this.duration,
    required this.appId,
    this.checkpointId,
    this.checkpointAssociations = const [],
  });

  factory TimelineModel.fromJson(Map<String, dynamic> json) {
    return TimelineModel(
      id: json['id'] as int,
      date: json['date'] != null ? DateTime.parse(json['date'] as String) : null,
      duration: json['duration'] as int?,
      appId: json['appId'] as int,
      checkpointId: json['checkpointId'] as int?,
      checkpointAssociations: json['checkpoint_associations'] != null
          ? List<int>.from(json['checkpoint_associations'] as List)
          : const [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date?.toIso8601String(),
      'duration': duration,
      'appId': appId,
      'checkpointId': checkpointId,
      'checkpoint_associations': checkpointAssociations,
    };
  }
}

class CheckpointModel {
  final int id;
  final String name;
  final String? description;
  final DateTime? createdAt;
  final DateTime validFrom;
  final String color;
  final int appId;
  final bool? isActive;
  final int? duration;
  final int? sessionsCount;
  final DateTime? lastUpdated;
  final DateTime? activatedAt;

  const CheckpointModel({
    required this.id,
    required this.name,
    this.description,
    this.createdAt,
    required this.validFrom,
    required this.color,
    required this.appId,
    this.isActive,
    this.duration,
    this.sessionsCount,
    this.lastUpdated,
    this.activatedAt,
  });

  factory CheckpointModel.fromJson(Map<String, dynamic> json) {
    return CheckpointModel(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      createdAt: json['created_at'] != null ? parseDate(json['created_at']) : null,
      validFrom: parseDate(json['valid_from'])!,
      color: json['color'] as String,
      appId: json['app_id'] as int,
      isActive: json['is_active'] as bool?,
      duration: json['duration'] as int?,
      sessionsCount: json['sessions_count'] as int?,
      lastUpdated: json['last_updated'] != null ? parseDate(json['last_updated']) : null,
      activatedAt: json['activated_at'] != null ? parseDate(json['activated_at']) : null,
    );
  }

  static DateTime? parseDate(dynamic dateValue) {
    if (dateValue == null) return null;

    // If it's already a DateTime, return it
    if (dateValue is DateTime) return dateValue;

    // If it's a number, treat it as a Unix timestamp
    if (dateValue is int || dateValue is double) {
      return DateTime.fromMillisecondsSinceEpoch((dateValue as num).toInt() * 1000);
    }

    // If it's not a string, convert it to string
    final dateString = dateValue.toString();

    // Try parsing as ISO 8601 format
    final isoDate = DateTime.tryParse(dateString);
    if (isoDate != null) return isoDate;

    // If that fails, print the format for debugging
    print('Failed to parse date: $dateString');
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'created_at': createdAt?.toIso8601String(),
      'valid_from': validFrom.toIso8601String(),
      'color': color,
      'app_id': appId,
      'is_active': isActive,
      'duration': duration,
      'sessions_count': sessionsCount,
      'last_updated': lastUpdated?.toIso8601String(),
      'activated_at': activatedAt?.toIso8601String(),
    };
  }

  CheckpointModel copyWith({
    int? id,
    String? name,
    String? description,
    DateTime? createdAt,
    DateTime? validFrom,
    String? color,
    int? appId,
    bool? isActive,
    int? duration,
    int? sessionsCount,
    DateTime? lastUpdated,
    DateTime? activatedAt,
  }) {
    return CheckpointModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      validFrom: validFrom ?? this.validFrom,
      color: color ?? this.color,
      appId: appId ?? this.appId,
      isActive: isActive ?? this.isActive,
      duration: duration ?? this.duration,
      sessionsCount: sessionsCount ?? this.sessionsCount,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      activatedAt: activatedAt ?? this.activatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CheckpointModel &&
        other.id == id &&
        other.name == name &&
        other.description == description &&
        other.createdAt == createdAt &&
        other.validFrom == validFrom &&
        other.color == color &&
        other.appId == appId &&
        other.isActive == isActive &&
        other.duration == duration &&
        other.sessionsCount == sessionsCount &&
        other.lastUpdated == lastUpdated &&
        other.activatedAt == activatedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      description,
      createdAt,
      validFrom,
      color,
      appId,
      isActive,
      duration,
      sessionsCount,
      lastUpdated,
      activatedAt,
    );
  }
}

class CheckpointDurationModel {
  final int id;
  final int? checkpointId;
  final int? appId;
  final int? duration;
  final int? sessionsCount;
  final DateTime? lastUpdated;

  const CheckpointDurationModel({
    required this.id,
    this.checkpointId,
    this.appId,
    this.duration,
    this.sessionsCount,
    this.lastUpdated,
  });

  factory CheckpointDurationModel.fromJson(Map<String, dynamic> json) {
    return CheckpointDurationModel(
      id: json['id'] as int,
      checkpointId: json['checkpoint_id'] as int?,
      appId: json['app_id'] as int?,
      duration: json['duration'] as int?,
      sessionsCount: json['sessions_count'] as int?,
      lastUpdated: CheckpointModel.parseDate(json['last_updated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'checkpoint_id': checkpointId,
      'app_id': appId,
      'duration': duration,
      'sessions_count': sessionsCount,
      'last_updated': lastUpdated?.toIso8601String(),
    };
  }
}

class TrackingStatus {
  final bool isTracking;
  final bool isPaused;
  final String? currentApp;
  final int currentSessionDuration;
  final DateTime? sessionStartTime;
  final int? currentCheckpointId;

  const TrackingStatus({
    this.isTracking = false,
    this.isPaused = false,
    this.currentApp,
    this.currentSessionDuration = 0,
    this.sessionStartTime,
    this.currentCheckpointId,
  });

  factory TrackingStatus.fromJson(Map<String, dynamic> json) {
    return TrackingStatus(
      isTracking: json['isTracking'] as bool? ?? false,
      isPaused: json['isPaused'] as bool? ?? false,
      currentApp: json['currentApp'] as String?,
      currentSessionDuration: json['currentSessionDuration'] as int? ?? 0,
      sessionStartTime: json['sessionStartTime'] != null
          ? DateTime.parse(json['sessionStartTime'] as String)
          : null,
      currentCheckpointId: json['currentCheckpointId'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isTracking': isTracking,
      'isPaused': isPaused,
      'currentApp': currentApp,
      'currentSessionDuration': currentSessionDuration,
      'sessionStartTime': sessionStartTime?.toIso8601String(),
      'currentCheckpointId': currentCheckpointId,
    };
  }
}

class AppStatistics {
  final AppModel app;
  final int totalDuration;
  final int todayDuration;
  final int weekDuration;
  final int monthDuration;
  final double averageSessionLength;
  final List<TimelineModel> recentSessions;

  const AppStatistics({
    required this.app,
    this.totalDuration = 0,
    this.todayDuration = 0,
    this.weekDuration = 0,
    this.monthDuration = 0,
    this.averageSessionLength = 0,
    this.recentSessions = const [],
  });

  factory AppStatistics.fromJson(Map<String, dynamic> json) {
    return AppStatistics(
      app: AppModel.fromJson(json['app'] as Map<String, dynamic>),
      totalDuration: json['totalDuration'] as int? ?? 0,
      todayDuration: json['todayDuration'] as int? ?? 0,
      weekDuration: json['weekDuration'] as int? ?? 0,
      monthDuration: json['monthDuration'] as int? ?? 0,
      averageSessionLength: (json['averageSessionLength'] as num?)?.toDouble() ?? 0,
      recentSessions: (json['recentSessions'] as List<dynamic>?)
              ?.map((e) => TimelineModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'app': app.toJson(),
      'totalDuration': totalDuration,
      'todayDuration': todayDuration,
      'weekDuration': weekDuration,
      'monthDuration': monthDuration,
      'averageSessionLength': averageSessionLength,
      'recentSessions': recentSessions.map((e) => e.toJson()).toList(),
    };
  }
}

class CheckpointWithStatus {
  final CheckpointModel checkpoint;
  final bool isActive;

  const CheckpointWithStatus({
    required this.checkpoint,
    required this.isActive,
  });

  factory CheckpointWithStatus.fromJson(Map<String, dynamic> json) {
    return CheckpointWithStatus(
      checkpoint: CheckpointModel.fromJson(json),
      isActive: json['is_active'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      ...checkpoint.toJson(),
      'is_active': isActive,
    };
  }

  CheckpointWithStatus copyWith({
    CheckpointModel? checkpoint,
    bool? isActive,
  }) {
    return CheckpointWithStatus(
      checkpoint: checkpoint ?? this.checkpoint,
      isActive: isActive ?? this.isActive,
    );
  }

  // Convenience getters
  int get id => checkpoint.id;
  String get name => checkpoint.name;
  String? get description => checkpoint.description;
  DateTime? get createdAt => checkpoint.createdAt;
  DateTime get validFrom => checkpoint.validFrom;
  String get color => checkpoint.color;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CheckpointWithStatus &&
        other.checkpoint == checkpoint &&
        other.isActive == isActive;
  }

  @override
  int get hashCode {
    return Object.hash(checkpoint, isActive);
  }
}
