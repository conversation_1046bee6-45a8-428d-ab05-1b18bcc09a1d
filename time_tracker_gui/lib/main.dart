import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'app/design_system/design_system.dart';
import 'presentation/providers/theme_provider.dart';
import 'presentation/providers/service_providers.dart';
import 'presentation/pages/home_page.dart';
import 'core/constants/api_constants.dart';

void main() {
  // Initialize sqflite database factory for desktop platforms
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }
  
  runApp(const ProviderScope(child: TimeTrackerApp()));
}

class TimeTrackerApp extends ConsumerWidget {
  const TimeTrackerApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeModeProvider);

    // Watch the settings change provider to activate it
    ref.watch(settingsChangeProvider);

    return ScreenUtilInit(
      designSize: const Size(1920, 1080), // Design size for desktop
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          title: AppConstants.appName,
          theme: AppThemeV2.lightTheme,
          darkTheme: AppThemeV2.darkTheme,
          themeMode: themeMode,
          home: const HomePage(),
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}


